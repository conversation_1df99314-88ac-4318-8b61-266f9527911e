{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_45009d28.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_45009d28-module__PnsN8q__className\",\n  \"variable\": \"geist_45009d28-module__PnsN8q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_45009d28.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22display%22:%22swap%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON> } from \"next/font/google\";\nimport { ThemeProvider } from \"next-themes\";\nimport \"./globals.css\";\n\nconst defaultUrl = process.env.VERCEL_URL\n  ? `https://${process.env.VERCEL_URL}`\n  : \"http://localhost:3000\";\n\nexport const metadata: Metadata = {\n  metadataBase: new URL(defaultUrl),\n  title: \"Next.js and Supabase Starter Kit\",\n  description: \"The fastest way to build apps with Next.js and Supabase\",\n};\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  display: \"swap\",\n  subsets: [\"latin\"],\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className={`${geistSans.className} antialiased`}>\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,GACrC,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,GACnC;AAEG,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI;IACtB,OAAO;IACP,aAAa;AACf;AAQe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC,YAAY,CAAC;sBACnD,cAAA,8OAAC,gJAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAExB;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/node_modules/next-themes/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/node_modules/next-themes/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-themes/dist/index.mjs\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}