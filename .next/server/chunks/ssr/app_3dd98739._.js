module.exports = {

"[project]/app/twitter-image.png (static in ecmascript)": ((__turbopack_context__) => {

__turbopack_context__.v("/_next/static/media/twitter-image.48058012.png");}),
"[project]/app/twitter-image.png.mjs { IMAGE => \"[project]/app/twitter-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$twitter$2d$image$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/app/twitter-image.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$twitter$2d$image$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 1200,
    height: 600
};
}),

};

//# sourceMappingURL=app_3dd98739._.js.map