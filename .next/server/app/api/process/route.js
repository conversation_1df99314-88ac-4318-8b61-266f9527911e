(()=>{var a={};a.id=697,a.ids=[697],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},27910:a=>{"use strict";a.exports=require("stream")},28086:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(9866),e=c(44999);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},76926:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},77940:(a,b,c)=>{"use strict";let d,e,f,g;c.r(b),c.d(b,{handler:()=>dv,patchFetch:()=>du,routeModule:()=>dq,serverHooks:()=>dt,workAsyncStorage:()=>dr,workUnitAsyncStorage:()=>ds});var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ab,ac,ad,ae,af,ag,ah,ai,aj,ak,al,am,an,ao,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD={};c.r(aD),c.d(aD,{POST:()=>dp});var aE=c(96559),aF=c(48088),aG=c(37719),aH=c(26191),aI=c(81289),aJ=c(261),aK=c(92603),aL=c(39893),aM=c(14823),aN=c(47220),aO=c(66946),aP=c(47912),aQ=c(99786),aR=c(46143),aS=c(86439),aT=c(43365),aU=c(32190),aV=c(28086);function aW(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c}function aX(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)}let aY=function(){let{crypto:a}=globalThis;if(a?.randomUUID)return aY=a.randomUUID.bind(a),a.randomUUID();let b=new Uint8Array(1),c=a?()=>a.getRandomValues(b)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,a=>(a^c()&15>>a/4).toString(16))};function aZ(a){return"object"==typeof a&&null!==a&&("name"in a&&"AbortError"===a.name||"message"in a&&String(a.message).includes("FetchRequestCanceledException"))}let a$=a=>{if(a instanceof Error)return a;if("object"==typeof a&&null!==a){try{if("[object Error]"===Object.prototype.toString.call(a)){let b=Error(a.message,a.cause?{cause:a.cause}:{});return a.stack&&(b.stack=a.stack),a.cause&&!b.cause&&(b.cause=a.cause),a.name&&(b.name=a.name),b}}catch{}try{return Error(JSON.stringify(a))}catch{}}return Error(a)};class a_ extends Error{}class a0 extends a_{constructor(a,b,c,d){super(`${a0.makeMessage(a,b,c)}`),this.status=a,this.headers=d,this.requestID=d?.get("x-request-id"),this.error=b,this.code=b?.code,this.param=b?.param,this.type=b?.type}static makeMessage(a,b,c){let d=b?.message?"string"==typeof b.message?b.message:JSON.stringify(b.message):b?JSON.stringify(b):c;return a&&d?`${a} ${d}`:a?`${a} status code (no body)`:d||"(no status code or body)"}static generate(a,b,c,d){if(!a||!d)return new a2({message:c,cause:a$(b)});let e=b?.error;return 400===a?new a4(a,e,c,d):401===a?new a5(a,e,c,d):403===a?new a6(a,e,c,d):404===a?new a7(a,e,c,d):409===a?new a8(a,e,c,d):422===a?new a9(a,e,c,d):429===a?new ba(a,e,c,d):a>=500?new bb(a,e,c,d):new a0(a,e,c,d)}}class a1 extends a0{constructor({message:a}={}){super(void 0,void 0,a||"Request was aborted.",void 0)}}class a2 extends a0{constructor({message:a,cause:b}){super(void 0,void 0,a||"Connection error.",void 0),b&&(this.cause=b)}}class a3 extends a2{constructor({message:a}={}){super({message:a??"Request timed out."})}}class a4 extends a0{}class a5 extends a0{}class a6 extends a0{}class a7 extends a0{}class a8 extends a0{}class a9 extends a0{}class ba extends a0{}class bb extends a0{}class bc extends a_{constructor(){super("Could not parse response content as the length limit was reached")}}class bd extends a_{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class be extends Error{constructor(a){super(a)}}let bf=/^[a-z][a-z0-9+.-]*:/i,bg=a=>(bg=Array.isArray)(a),bh=bg;function bi(a){return null!=a&&"object"==typeof a&&!Array.isArray(a)}let bj=a=>new Promise(b=>setTimeout(b,a)),bk="5.10.0",bl=a=>"x32"===a?"x32":"x86_64"===a||"x64"===a?"x64":"arm"===a?"arm":"aarch64"===a||"arm64"===a?"arm64":a?`other:${a}`:"unknown",bm=a=>(a=a.toLowerCase()).includes("ios")?"iOS":"android"===a?"Android":"darwin"===a?"MacOS":"win32"===a?"Windows":"freebsd"===a?"FreeBSD":"openbsd"===a?"OpenBSD":"linux"===a?"Linux":a?`Other:${a}`:"Unknown";function bn(...a){let b=globalThis.ReadableStream;if(void 0===b)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new b(...a)}function bo(a){let b=Symbol.asyncIterator in a?a[Symbol.asyncIterator]():a[Symbol.iterator]();return bn({start(){},async pull(a){let{done:c,value:d}=await b.next();c?a.close():a.enqueue(d)},async cancel(){await b.return?.()}})}function bp(a){if(a[Symbol.asyncIterator])return a;let b=a.getReader();return{async next(){try{let a=await b.read();return a?.done&&b.releaseLock(),a}catch(a){throw b.releaseLock(),a}},async return(){let a=b.cancel();return b.releaseLock(),await a,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function bq(a){if(null===a||"object"!=typeof a)return;if(a[Symbol.asyncIterator])return void await a[Symbol.asyncIterator]().return?.();let b=a.getReader(),c=b.cancel();b.releaseLock(),await c}let br=({headers:a,body:b})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(b)}),bs="RFC3986",bt=a=>String(a),bu={RFC1738:a=>String(a).replace(/%20/g,"+"),RFC3986:bt},bv=(a,b)=>(bv=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(a,b),bw=(()=>{let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a})();function bx(a,b){if(bg(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}let by={brackets:a=>String(a)+"[]",comma:"comma",indices:(a,b)=>String(a)+"["+b+"]",repeat:a=>String(a)},bz=function(a,b){Array.prototype.push.apply(a,bg(b)?b:[b])},bA={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(a,b,c,d,e)=>{if(0===a.length)return a;let f=a;if("symbol"==typeof a?f=Symbol.prototype.toString.call(a):"string"!=typeof a&&(f=String(a)),"iso-8859-1"===c)return escape(f).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let g="";for(let a=0;a<f.length;a+=1024){let b=f.length>=1024?f.slice(a,a+1024):f,c=[];for(let a=0;a<b.length;++a){let d=b.charCodeAt(a);if(45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||"RFC1738"===e&&(40===d||41===d)){c[c.length]=b.charAt(a);continue}if(d<128){c[c.length]=bw[d];continue}if(d<2048){c[c.length]=bw[192|d>>6]+bw[128|63&d];continue}if(d<55296||d>=57344){c[c.length]=bw[224|d>>12]+bw[128|d>>6&63]+bw[128|63&d];continue}a+=1,d=65536+((1023&d)<<10|1023&b.charCodeAt(a)),c[c.length]=bw[240|d>>18]+bw[128|d>>12&63]+bw[128|d>>6&63]+bw[128|63&d]}g+=c.join("")}return g},encodeValuesOnly:!1,format:bs,formatter:bt,indices:!1,serializeDate:a=>(e??(e=Function.prototype.call.bind(Date.prototype.toISOString)))(a),skipNulls:!1,strictNullHandling:!1},bB={};function bC(a){let b;return(f??(f=(b=new globalThis.TextEncoder).encode.bind(b)))(a)}function bD(a){let b;return(g??(g=(b=new globalThis.TextDecoder).decode.bind(b)))(a)}class bE{constructor(){h.set(this,void 0),i.set(this,void 0),aW(this,h,new Uint8Array,"f"),aW(this,i,null,"f")}decode(a){let b;if(null==a)return[];let c=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?bC(a):a;aW(this,h,function(a){let b=0;for(let c of a)b+=c.length;let c=new Uint8Array(b),d=0;for(let b of a)c.set(b,d),d+=b.length;return c}([aX(this,h,"f"),c]),"f");let d=[];for(;null!=(b=function(a,b){for(let c=b??0;c<a.length;c++){if(10===a[c])return{preceding:c,index:c+1,carriage:!1};if(13===a[c])return{preceding:c,index:c+1,carriage:!0}}return null}(aX(this,h,"f"),aX(this,i,"f")));){if(b.carriage&&null==aX(this,i,"f")){aW(this,i,b.index,"f");continue}if(null!=aX(this,i,"f")&&(b.index!==aX(this,i,"f")+1||b.carriage)){d.push(bD(aX(this,h,"f").subarray(0,aX(this,i,"f")-1))),aW(this,h,aX(this,h,"f").subarray(aX(this,i,"f")),"f"),aW(this,i,null,"f");continue}let a=null!==aX(this,i,"f")?b.preceding-1:b.preceding,c=bD(aX(this,h,"f").subarray(0,a));d.push(c),aW(this,h,aX(this,h,"f").subarray(b.index),"f"),aW(this,i,null,"f")}return d}flush(){return aX(this,h,"f").length?this.decode("\n"):[]}}h=new WeakMap,i=new WeakMap,bE.NEWLINE_CHARS=new Set(["\n","\r"]),bE.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let bF={off:0,error:200,warn:300,info:400,debug:500},bG=(a,b,c)=>{if(a){if(Object.prototype.hasOwnProperty.call(bF,a))return a;bL(c).warn(`${b} was set to ${JSON.stringify(a)}, expected one of ${JSON.stringify(Object.keys(bF))}`)}};function bH(){}function bI(a,b,c){return!b||bF[a]>bF[c]?bH:b[a].bind(b)}let bJ={error:bH,warn:bH,info:bH,debug:bH},bK=new WeakMap;function bL(a){let b=a.logger,c=a.logLevel??"off";if(!b)return bJ;let d=bK.get(b);if(d&&d[0]===c)return d[1];let e={error:bI("error",b,c),warn:bI("warn",b,c),info:bI("info",b,c),debug:bI("debug",b,c)};return bK.set(b,[c,e]),e}let bM=a=>(a.options&&(a.options={...a.options},delete a.options.headers),a.headers&&(a.headers=Object.fromEntries((a.headers instanceof Headers?[...a.headers]:Object.entries(a.headers)).map(([a,b])=>[a,"authorization"===a.toLowerCase()||"cookie"===a.toLowerCase()||"set-cookie"===a.toLowerCase()?"***":b]))),"retryOfRequestLogID"in a&&(a.retryOfRequestLogID&&(a.retryOf=a.retryOfRequestLogID),delete a.retryOfRequestLogID),a);class bN{constructor(a,b,c){this.iterator=a,j.set(this,void 0),this.controller=b,aW(this,j,c,"f")}static fromSSEResponse(a,b,c){let d=!1,e=c?bL(c):console;async function*f(){if(d)throw new a_("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let c=!1;try{for await(let d of bO(a,b))if(!c){if(d.data.startsWith("[DONE]")){c=!0;continue}if(null===d.event||d.event.startsWith("response.")||d.event.startsWith("image_edit.")||d.event.startsWith("image_generation.")||d.event.startsWith("transcript.")){let b;try{b=JSON.parse(d.data)}catch(a){throw e.error("Could not parse message into JSON:",d.data),e.error("From chunk:",d.raw),a}if(b&&b.error)throw new a0(void 0,b.error,void 0,a.headers);yield b}else{let a;try{a=JSON.parse(d.data)}catch(a){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),a}if("error"==d.event)throw new a0(void 0,a.error,a.message,void 0);yield{event:d.event,data:a}}}c=!0}catch(a){if(aZ(a))return;throw a}finally{c||b.abort()}}return new bN(f,b,c)}static fromReadableStream(a,b,c){let d=!1;async function*e(){let b=new bE;for await(let c of bp(a))for(let a of b.decode(c))yield a;for(let a of b.flush())yield a}return new bN(async function*(){if(d)throw new a_("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let a=!1;try{for await(let b of e())!a&&b&&(yield JSON.parse(b));a=!0}catch(a){if(aZ(a))return;throw a}finally{a||b.abort()}},b,c)}[(j=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let a=[],b=[],c=this.iterator(),d=d=>({next:()=>{if(0===d.length){let d=c.next();a.push(d),b.push(d)}return d.shift()}});return[new bN(()=>d(a),this.controller,aX(this,j,"f")),new bN(()=>d(b),this.controller,aX(this,j,"f"))]}toReadableStream(){let a,b=this;return bn({async start(){a=b[Symbol.asyncIterator]()},async pull(b){try{let{value:c,done:d}=await a.next();if(d)return b.close();let e=bC(JSON.stringify(c)+"\n");b.enqueue(e)}catch(a){b.error(a)}},async cancel(){await a.return?.()}})}}async function*bO(a,b){if(!a.body){if(b.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new a_("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new a_("Attempted to iterate over a response with no body")}let c=new bQ,d=new bE;for await(let b of bP(bp(a.body)))for(let a of d.decode(b)){let b=c.decode(a);b&&(yield b)}for(let a of d.flush()){let b=c.decode(a);b&&(yield b)}}async function*bP(a){let b=new Uint8Array;for await(let c of a){let a;if(null==c)continue;let d=c instanceof ArrayBuffer?new Uint8Array(c):"string"==typeof c?bC(c):c,e=new Uint8Array(b.length+d.length);for(e.set(b),e.set(d,b.length),b=e;-1!==(a=function(a){for(let b=0;b<a.length-1;b++){if(10===a[b]&&10===a[b+1]||13===a[b]&&13===a[b+1])return b+2;if(13===a[b]&&10===a[b+1]&&b+3<a.length&&13===a[b+2]&&10===a[b+3])return b+4}return -1}(b));)yield b.slice(0,a),b=b.slice(a)}b.length>0&&(yield b)}class bQ{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(a){if(a.endsWith("\r")&&(a=a.substring(0,a.length-1)),!a){if(!this.event&&!this.data.length)return null;let a={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(a),a.startsWith(":"))return null;let[b,c,d]=function(a,b){let c=a.indexOf(":");return -1!==c?[a.substring(0,c),b,a.substring(c+b.length)]:[a,"",""]}(a,":");return d.startsWith(" ")&&(d=d.substring(1)),"event"===b?this.event=d:"data"===b&&this.data.push(d),null}}async function bR(a,b){let{response:c,requestLogID:d,retryOfRequestLogID:e,startTime:f}=b,g=await (async()=>{if(b.options.stream)return(bL(a).debug("response",c.status,c.url,c.headers,c.body),b.options.__streamClass)?b.options.__streamClass.fromSSEResponse(c,b.controller,a):bN.fromSSEResponse(c,b.controller,a);if(204===c.status)return null;if(b.options.__binaryResponse)return c;let d=c.headers.get("content-type"),e=d?.split(";")[0]?.trim();return e?.includes("application/json")||e?.endsWith("+json")?bS(await c.json(),c):await c.text()})();return bL(a).debug(`[${d}] response parsed`,bM({retryOfRequestLogID:e,url:c.url,status:c.status,body:g,durationMs:Date.now()-f})),g}function bS(a,b){return!a||"object"!=typeof a||Array.isArray(a)?a:Object.defineProperty(a,"_request_id",{value:b.headers.get("x-request-id"),enumerable:!1})}class bT extends Promise{constructor(a,b,c=bR){super(a=>{a(null)}),this.responsePromise=b,this.parseResponse=c,k.set(this,void 0),aW(this,k,a,"f")}_thenUnwrap(a){return new bT(aX(this,k,"f"),this.responsePromise,async(b,c)=>bS(a(await this.parseResponse(b,c),c),c.response))}asResponse(){return this.responsePromise.then(a=>a.response)}async withResponse(){let[a,b]=await Promise.all([this.parse(),this.asResponse()]);return{data:a,response:b,request_id:b.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(a=>this.parseResponse(aX(this,k,"f"),a))),this.parsedPromise}then(a,b){return this.parse().then(a,b)}catch(a){return this.parse().catch(a)}finally(a){return this.parse().finally(a)}}k=new WeakMap;class bU{constructor(a,b,c,d){l.set(this,void 0),aW(this,l,a,"f"),this.options=d,this.response=b,this.body=c}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let a=this.nextPageRequestOptions();if(!a)throw new a_("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await aX(this,l,"f").requestAPIList(this.constructor,a)}async *iterPages(){let a=this;for(yield a;a.hasNextPage();)a=await a.getNextPage(),yield a}async *[(l=new WeakMap,Symbol.asyncIterator)](){for await(let a of this.iterPages())for(let b of a.getPaginatedItems())yield b}}class bV extends bT{constructor(a,b,c){super(a,b,async(a,b)=>new c(a,b.response,await bR(a,b),b.options))}async *[Symbol.asyncIterator](){for await(let a of(await this))yield a}}class bW extends bU{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.object=c.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class bX extends bU{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.has_more=c.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var a;let b=this.getPaginatedItems(),c=b[b.length-1]?.id;return c?{...this.options,query:{..."object"!=typeof(a=this.options.query)?{}:a??{},after:c}}:null}}let bY=()=>{if("undefined"==typeof File){let{process:a}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof a?.versions?.node&&20>parseInt(a.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function bZ(a,b,c){return bY(),new File(a,b??"unknown_file",c)}function b$(a){return("object"==typeof a&&null!==a&&("name"in a&&a.name&&String(a.name)||"url"in a&&a.url&&String(a.url)||"filename"in a&&a.filename&&String(a.filename)||"path"in a&&a.path&&String(a.path))||"").split(/[\\/]/).pop()||void 0}let b_=a=>null!=a&&"object"==typeof a&&"function"==typeof a[Symbol.asyncIterator],b0=async(a,b)=>({...a,body:await b2(a.body,b)}),b1=new WeakMap,b2=async(a,b)=>{if(!await function(a){let b="function"==typeof a?a:a.fetch,c=b1.get(b);if(c)return c;let d=(async()=>{try{let a="Response"in b?b.Response:(await b("data:,")).constructor,c=new FormData;if(c.toString()===await new a(c).text())return!1;return!0}catch{return!0}})();return b1.set(b,d),d}(b))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let c=new FormData;return await Promise.all(Object.entries(a||{}).map(([a,b])=>b5(c,a,b))),c},b3=a=>a instanceof Blob&&"name"in a,b4=a=>{if((a=>"object"==typeof a&&null!==a&&(a instanceof Response||b_(a)||b3(a)))(a))return!0;if(Array.isArray(a))return a.some(b4);if(a&&"object"==typeof a){for(let b in a)if(b4(a[b]))return!0}return!1},b5=async(a,b,c)=>{if(void 0!==c){if(null==c)throw TypeError(`Received null for "${b}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof c||"number"==typeof c||"boolean"==typeof c)a.append(b,String(c));else if(c instanceof Response)a.append(b,bZ([await c.blob()],b$(c)));else if(b_(c))a.append(b,bZ([await new Response(bo(c)).blob()],b$(c)));else if(b3(c))a.append(b,c,b$(c));else if(Array.isArray(c))await Promise.all(c.map(c=>b5(a,b+"[]",c)));else if("object"==typeof c)await Promise.all(Object.entries(c).map(([c,d])=>b5(a,`${b}[${c}]`,d)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},b6=a=>null!=a&&"object"==typeof a&&"number"==typeof a.size&&"string"==typeof a.type&&"function"==typeof a.text&&"function"==typeof a.slice&&"function"==typeof a.arrayBuffer;async function b7(a,b,c){let d,e;if(bY(),null!=(d=a=await a)&&"object"==typeof d&&"string"==typeof d.name&&"number"==typeof d.lastModified&&b6(d))return a instanceof File?a:bZ([await a.arrayBuffer()],a.name);if(null!=(e=a)&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob){let d=await a.blob();return b||(b=new URL(a.url).pathname.split(/[\\/]/).pop()),bZ(await b8(d),b,c)}let f=await b8(a);if(b||(b=b$(a)),!c?.type){let a=f.find(a=>"object"==typeof a&&"type"in a&&a.type);"string"==typeof a&&(c={...c,type:a})}return bZ(f,b,c)}async function b8(a){let b=[];if("string"==typeof a||ArrayBuffer.isView(a)||a instanceof ArrayBuffer)b.push(a);else if(b6(a))b.push(a instanceof Blob?a:await a.arrayBuffer());else if(b_(a))for await(let c of a)b.push(...await b8(c));else{let b=a?.constructor?.name;throw Error(`Unexpected data type: ${typeof a}${b?`; constructor: ${b}`:""}${function(a){if("object"!=typeof a||null===a)return"";let b=Object.getOwnPropertyNames(a);return`; props: [${b.map(a=>`"${a}"`).join(", ")}]`}(a)}`)}return b}class b9{constructor(a){this._client=a}}function ca(a){return a.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let cb=Object.freeze(Object.create(null)),cc=((a=ca)=>function(b,...c){let d;if(1===b.length)return b[0];let e=!1,f=[],g=b.reduce((b,d,g)=>{/[?#]/.test(d)&&(e=!0);let h=c[g],i=(e?encodeURIComponent:a)(""+h);return g!==c.length&&(null==h||"object"==typeof h&&h.toString===Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??cb)??cb)?.toString)&&(i=h+"",f.push({start:b.length+d.length,length:i.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),b+d+(g===c.length?"":i)},""),h=g.split(/[?#]/,1)[0],i=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(d=i.exec(h));)f.push({start:d.index,length:d[0].length,error:`Value "${d[0]}" can't be safely passed as a path parameter`});if(f.sort((a,b)=>a.start-b.start),f.length>0){let a=0,b=f.reduce((b,c)=>{let d=" ".repeat(c.start-a),e="^".repeat(c.length);return a=c.start+c.length,b+d+e},"");throw new a_(`Path parameters result in path with invalid segments:
${f.map(a=>a.error).join("\n")}
${g}
${b}`)}return g})(ca);class cd extends b9{list(a,b={},c){return this._client.getAPIList(cc`/chat/completions/${a}/messages`,bX,{query:b,...c})}}let ce=a=>a?.role==="assistant",cf=a=>a?.role==="tool";class cg{constructor(){m.add(this),this.controller=new AbortController,n.set(this,void 0),o.set(this,()=>{}),p.set(this,()=>{}),q.set(this,void 0),r.set(this,()=>{}),s.set(this,()=>{}),t.set(this,{}),u.set(this,!1),v.set(this,!1),w.set(this,!1),x.set(this,!1),aW(this,n,new Promise((a,b)=>{aW(this,o,a,"f"),aW(this,p,b,"f")}),"f"),aW(this,q,new Promise((a,b)=>{aW(this,r,a,"f"),aW(this,s,b,"f")}),"f"),aX(this,n,"f").catch(()=>{}),aX(this,q,"f").catch(()=>{})}_run(a){setTimeout(()=>{a().then(()=>{this._emitFinal(),this._emit("end")},aX(this,m,"m",y).bind(this))},0)}_connected(){this.ended||(aX(this,o,"f").call(this),this._emit("connect"))}get ended(){return aX(this,u,"f")}get errored(){return aX(this,v,"f")}get aborted(){return aX(this,w,"f")}abort(){this.controller.abort()}on(a,b){return(aX(this,t,"f")[a]||(aX(this,t,"f")[a]=[])).push({listener:b}),this}off(a,b){let c=aX(this,t,"f")[a];if(!c)return this;let d=c.findIndex(a=>a.listener===b);return d>=0&&c.splice(d,1),this}once(a,b){return(aX(this,t,"f")[a]||(aX(this,t,"f")[a]=[])).push({listener:b,once:!0}),this}emitted(a){return new Promise((b,c)=>{aW(this,x,!0,"f"),"error"!==a&&this.once("error",c),this.once(a,b)})}async done(){aW(this,x,!0,"f"),await aX(this,q,"f")}_emit(a,...b){if(aX(this,u,"f"))return;"end"===a&&(aW(this,u,!0,"f"),aX(this,r,"f").call(this));let c=aX(this,t,"f")[a];if(c&&(aX(this,t,"f")[a]=c.filter(a=>!a.once),c.forEach(({listener:a})=>a(...b))),"abort"===a){let a=b[0];aX(this,x,"f")||c?.length||Promise.reject(a),aX(this,p,"f").call(this,a),aX(this,s,"f").call(this,a),this._emit("end");return}if("error"===a){let a=b[0];aX(this,x,"f")||c?.length||Promise.reject(a),aX(this,p,"f").call(this,a),aX(this,s,"f").call(this,a),this._emit("end")}}_emitFinal(){}}function ch(a){return a?.$brand==="auto-parseable-response-format"}function ci(a){return a?.$brand==="auto-parseable-tool"}function cj(a,b){let c=a.choices.map(a=>{var c,d;if("length"===a.finish_reason)throw new bc;if("content_filter"===a.finish_reason)throw new bd;return{...a,message:{...a.message,...a.message.tool_calls?{tool_calls:a.message.tool_calls?.map(a=>(function(a,b){let c=a.tools?.find(a=>a.function?.name===b.function.name);return{...b,function:{...b.function,parsed_arguments:ci(c)?c.$parseRaw(b.function.arguments):c?.function.strict?JSON.parse(b.function.arguments):null}}})(b,a))??void 0}:void 0,parsed:a.message.content&&!a.message.refusal?(c=b,d=a.message.content,c.response_format?.type!=="json_schema"?null:c.response_format?.type==="json_schema"?"$parseRaw"in c.response_format?c.response_format.$parseRaw(d):JSON.parse(d):null):null}}});return{...a,choices:c}}function ck(a){return!!ch(a.response_format)||(a.tools?.some(a=>ci(a)||"function"===a.type&&!0===a.function.strict)??!1)}n=new WeakMap,o=new WeakMap,p=new WeakMap,q=new WeakMap,r=new WeakMap,s=new WeakMap,t=new WeakMap,u=new WeakMap,v=new WeakMap,w=new WeakMap,x=new WeakMap,m=new WeakSet,y=function(a){if(aW(this,v,!0,"f"),a instanceof Error&&"AbortError"===a.name&&(a=new a1),a instanceof a1)return aW(this,w,!0,"f"),this._emit("abort",a);if(a instanceof a_)return this._emit("error",a);if(a instanceof Error){let b=new a_(a.message);return b.cause=a,this._emit("error",b)}return this._emit("error",new a_(String(a)))};class cl extends cg{constructor(){super(...arguments),z.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(a){this._chatCompletions.push(a),this._emit("chatCompletion",a);let b=a.choices[0]?.message;return b&&this._addMessage(b),a}_addMessage(a,b=!0){if("content"in a||(a.content=null),this.messages.push(a),b){if(this._emit("message",a),cf(a)&&a.content)this._emit("functionToolCallResult",a.content);else if(ce(a)&&a.tool_calls)for(let b of a.tool_calls)"function"===b.type&&this._emit("functionToolCall",b.function)}}async finalChatCompletion(){await this.done();let a=this._chatCompletions[this._chatCompletions.length-1];if(!a)throw new a_("stream ended without producing a ChatCompletion");return a}async finalContent(){return await this.done(),aX(this,z,"m",A).call(this)}async finalMessage(){return await this.done(),aX(this,z,"m",B).call(this)}async finalFunctionToolCall(){return await this.done(),aX(this,z,"m",C).call(this)}async finalFunctionToolCallResult(){return await this.done(),aX(this,z,"m",D).call(this)}async totalUsage(){return await this.done(),aX(this,z,"m",E).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let a=this._chatCompletions[this._chatCompletions.length-1];a&&this._emit("finalChatCompletion",a);let b=aX(this,z,"m",B).call(this);b&&this._emit("finalMessage",b);let c=aX(this,z,"m",A).call(this);c&&this._emit("finalContent",c);let d=aX(this,z,"m",C).call(this);d&&this._emit("finalFunctionToolCall",d);let e=aX(this,z,"m",D).call(this);null!=e&&this._emit("finalFunctionToolCallResult",e),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",aX(this,z,"m",E).call(this))}async _createChatCompletion(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aX(this,z,"m",F).call(this,b);let e=await a.chat.completions.create({...b,stream:!1},{...c,signal:this.controller.signal});return this._connected(),this._addChatCompletion(cj(e,b))}async _runChatCompletion(a,b,c){for(let a of b.messages)this._addMessage(a,!1);return await this._createChatCompletion(a,b,c)}async _runTools(a,b,c){let d="tool",{tool_choice:e="auto",stream:f,...g}=b,h="string"!=typeof e&&e?.function?.name,{maxChatCompletions:i=10}=c||{},j=b.tools.map(a=>{if(ci(a)){if(!a.$callback)throw new a_("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:a.$callback,name:a.function.name,description:a.function.description||"",parameters:a.function.parameters,parse:a.$parseRaw,strict:!0}}}return a}),k={};for(let a of j)"function"===a.type&&(k[a.function.name||a.function.function.name]=a.function);let l="tools"in b?j.map(a=>"function"===a.type?{type:"function",function:{name:a.function.name||a.function.function.name,parameters:a.function.parameters,description:a.function.description,strict:a.function.strict}}:a):void 0;for(let a of b.messages)this._addMessage(a,!1);for(let b=0;b<i;++b){let b=await this._createChatCompletion(a,{...g,tool_choice:e,tools:l,messages:[...this.messages]},c),f=b.choices[0]?.message;if(!f)throw new a_("missing message in ChatCompletion response");if(!f.tool_calls?.length)break;for(let a of f.tool_calls){let b;if("function"!==a.type)continue;let c=a.id,{name:e,arguments:f}=a.function,g=k[e];if(g){if(h&&h!==e){let a=`Invalid tool_call: ${JSON.stringify(e)}. ${JSON.stringify(h)} requested. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}}else{let a=`Invalid tool_call: ${JSON.stringify(e)}. Available options are: ${Object.keys(k).map(a=>JSON.stringify(a)).join(", ")}. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}try{b="function"==typeof g.parse?await g.parse(f):f}catch(b){let a=b instanceof Error?b.message:String(b);this._addMessage({role:d,tool_call_id:c,content:a});continue}let i=await g.function(b,this),j=aX(this,z,"m",G).call(this,i);if(this._addMessage({role:d,tool_call_id:c,content:j}),h)return}}}}z=new WeakSet,A=function(){return aX(this,z,"m",B).call(this).content??null},B=function(){let a=this.messages.length;for(;a-- >0;){let b=this.messages[a];if(ce(b))return{...b,content:b.content??null,refusal:b.refusal??null}}throw new a_("stream ended without producing a ChatCompletionMessage with role=assistant")},C=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(ce(b)&&b?.tool_calls?.length)return b.tool_calls.at(-1)?.function}},D=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(cf(b)&&null!=b.content&&"string"==typeof b.content&&this.messages.some(a=>"assistant"===a.role&&a.tool_calls?.some(a=>"function"===a.type&&a.id===b.tool_call_id)))return b.content}},E=function(){let a={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:b}of this._chatCompletions)b&&(a.completion_tokens+=b.completion_tokens,a.prompt_tokens+=b.prompt_tokens,a.total_tokens+=b.total_tokens);return a},F=function(a){if(null!=a.n&&a.n>1)throw new a_("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},G=function(a){return"string"==typeof a?a:void 0===a?"undefined":JSON.stringify(a)};class cm extends cl{static runTools(a,b,c){let d=new cm,e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}_addMessage(a,b=!0){super._addMessage(a,b),ce(a)&&a.content&&this._emit("content",a.content)}}let cn={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class co extends Error{}class cp extends Error{}let cq=a=>(function(a,b=cn.ALL){if("string"!=typeof a)throw TypeError(`expecting str, got ${typeof a}`);if(!a.trim())throw Error(`${a} is empty`);return((a,b)=>{let c=a.length,d=0,e=a=>{throw new co(`${a} at position ${d}`)},f=a=>{throw new cp(`${a} at position ${d}`)},g=()=>(l(),d>=c&&e("Unexpected end of input"),'"'===a[d])?h():"{"===a[d]?i():"["===a[d]?j():"null"===a.substring(d,d+4)||cn.NULL&b&&c-d<4&&"null".startsWith(a.substring(d))?(d+=4,null):"true"===a.substring(d,d+4)||cn.BOOL&b&&c-d<4&&"true".startsWith(a.substring(d))?(d+=4,!0):"false"===a.substring(d,d+5)||cn.BOOL&b&&c-d<5&&"false".startsWith(a.substring(d))?(d+=5,!1):"Infinity"===a.substring(d,d+8)||cn.INFINITY&b&&c-d<8&&"Infinity".startsWith(a.substring(d))?(d+=8,1/0):"-Infinity"===a.substring(d,d+9)||cn.MINUS_INFINITY&b&&1<c-d&&c-d<9&&"-Infinity".startsWith(a.substring(d))?(d+=9,-1/0):"NaN"===a.substring(d,d+3)||cn.NAN&b&&c-d<3&&"NaN".startsWith(a.substring(d))?(d+=3,NaN):k(),h=()=>{let g=d,h=!1;for(d++;d<c&&('"'!==a[d]||h&&"\\"===a[d-1]);)h="\\"===a[d]&&!h,d++;if('"'==a.charAt(d))try{return JSON.parse(a.substring(g,++d-Number(h)))}catch(a){f(String(a))}else if(cn.STR&b)try{return JSON.parse(a.substring(g,d-Number(h))+'"')}catch(b){return JSON.parse(a.substring(g,a.lastIndexOf("\\"))+'"')}e("Unterminated string literal")},i=()=>{d++,l();let f={};try{for(;"}"!==a[d];){if(l(),d>=c&&cn.OBJ&b)return f;let e=h();l(),d++;try{let a=g();Object.defineProperty(f,e,{value:a,writable:!0,enumerable:!0,configurable:!0})}catch(a){if(cn.OBJ&b)return f;throw a}l(),","===a[d]&&d++}}catch(a){if(cn.OBJ&b)return f;e("Expected '}' at end of object")}return d++,f},j=()=>{d++;let c=[];try{for(;"]"!==a[d];)c.push(g()),l(),","===a[d]&&d++}catch(a){if(cn.ARR&b)return c;e("Expected ']' at end of array")}return d++,c},k=()=>{if(0===d){"-"===a&&cn.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a)}catch(c){if(cn.NUM&b)try{if("."===a[a.length-1])return JSON.parse(a.substring(0,a.lastIndexOf(".")));return JSON.parse(a.substring(0,a.lastIndexOf("e")))}catch(a){}f(String(c))}}let g=d;for("-"===a[d]&&d++;a[d]&&!",]}".includes(a[d]);)d++;d!=c||cn.NUM&b||e("Unterminated number literal");try{return JSON.parse(a.substring(g,d))}catch(c){"-"===a.substring(g,d)&&cn.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a.substring(g,a.lastIndexOf("e")))}catch(a){f(String(a))}}},l=()=>{for(;d<c&&" \n\r	".includes(a[d]);)d++};return g()})(a.trim(),b)})(a,cn.ALL^cn.NUM);class cr extends cl{constructor(a){super(),H.add(this),I.set(this,void 0),J.set(this,void 0),K.set(this,void 0),aW(this,I,a,"f"),aW(this,J,[],"f")}get currentChatCompletionSnapshot(){return aX(this,K,"f")}static fromReadableStream(a){let b=new cr(null);return b._run(()=>b._fromReadableStream(a)),b}static createChatCompletion(a,b,c){let d=new cr(b);return d._run(()=>d._runChatCompletion(a,{...b,stream:!0},{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createChatCompletion(a,b,c){super._createChatCompletion;let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aX(this,H,"m",L).call(this);let e=await a.chat.completions.create({...b,stream:!0},{...c,signal:this.controller.signal});for await(let a of(this._connected(),e))aX(this,H,"m",N).call(this,a);if(e.controller.signal?.aborted)throw new a1;return this._addChatCompletion(aX(this,H,"m",Q).call(this))}async _fromReadableStream(a,b){let c,d=b?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aX(this,H,"m",L).call(this),this._connected();let e=bN.fromReadableStream(a,this.controller);for await(let a of e)c&&c!==a.id&&this._addChatCompletion(aX(this,H,"m",Q).call(this)),aX(this,H,"m",N).call(this,a),c=a.id;if(e.controller.signal?.aborted)throw new a1;return this._addChatCompletion(aX(this,H,"m",Q).call(this))}[(I=new WeakMap,J=new WeakMap,K=new WeakMap,H=new WeakSet,L=function(){this.ended||aW(this,K,void 0,"f")},M=function(a){let b=aX(this,J,"f")[a.index];return b||(b={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},aX(this,J,"f")[a.index]=b),b},N=function(a){if(this.ended)return;let b=aX(this,H,"m",S).call(this,a);for(let c of(this._emit("chunk",a,b),a.choices)){let a=b.choices[c.index];null!=c.delta.content&&a.message?.role==="assistant"&&a.message?.content&&(this._emit("content",c.delta.content,a.message.content),this._emit("content.delta",{delta:c.delta.content,snapshot:a.message.content,parsed:a.message.parsed})),null!=c.delta.refusal&&a.message?.role==="assistant"&&a.message?.refusal&&this._emit("refusal.delta",{delta:c.delta.refusal,snapshot:a.message.refusal}),c.logprobs?.content!=null&&a.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:c.logprobs?.content,snapshot:a.logprobs?.content??[]}),c.logprobs?.refusal!=null&&a.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:c.logprobs?.refusal,snapshot:a.logprobs?.refusal??[]});let d=aX(this,H,"m",M).call(this,a);for(let b of(a.finish_reason&&(aX(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aX(this,H,"m",O).call(this,a,d.current_tool_call_index)),c.delta.tool_calls??[]))d.current_tool_call_index!==b.index&&(aX(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aX(this,H,"m",O).call(this,a,d.current_tool_call_index)),d.current_tool_call_index=b.index;for(let b of c.delta.tool_calls??[]){let c=a.message.tool_calls?.[b.index];c?.type&&(c?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:c.function?.name,index:b.index,arguments:c.function.arguments,parsed_arguments:c.function.parsed_arguments,arguments_delta:b.function?.arguments??""}):c?.type)}}},O=function(a,b){if(aX(this,H,"m",M).call(this,a).done_tool_calls.has(b))return;let c=a.message.tool_calls?.[b];if(!c)throw Error("no tool call snapshot");if(!c.type)throw Error("tool call snapshot missing `type`");if("function"===c.type){let a=aX(this,I,"f")?.tools?.find(a=>"function"===a.type&&a.function.name===c.function.name);this._emit("tool_calls.function.arguments.done",{name:c.function.name,index:b,arguments:c.function.arguments,parsed_arguments:ci(a)?a.$parseRaw(c.function.arguments):a?.function.strict?JSON.parse(c.function.arguments):null})}else c.type},P=function(a){let b=aX(this,H,"m",M).call(this,a);if(a.message.content&&!b.content_done){b.content_done=!0;let c=aX(this,H,"m",R).call(this);this._emit("content.done",{content:a.message.content,parsed:c?c.$parseRaw(a.message.content):null})}a.message.refusal&&!b.refusal_done&&(b.refusal_done=!0,this._emit("refusal.done",{refusal:a.message.refusal})),a.logprobs?.content&&!b.logprobs_content_done&&(b.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:a.logprobs.content})),a.logprobs?.refusal&&!b.logprobs_refusal_done&&(b.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:a.logprobs.refusal}))},Q=function(){if(this.ended)throw new a_("stream has ended, this shouldn't happen");let a=aX(this,K,"f");if(!a)throw new a_("request ended without sending any chunks");return aW(this,K,void 0,"f"),aW(this,J,[],"f"),function(a,b){var c;let{id:d,choices:e,created:f,model:g,system_fingerprint:h,...i}=a;return c={...i,id:d,choices:e.map(({message:b,finish_reason:c,index:d,logprobs:e,...f})=>{if(!c)throw new a_(`missing finish_reason for choice ${d}`);let{content:g=null,function_call:h,tool_calls:i,...j}=b,k=b.role;if(!k)throw new a_(`missing role for choice ${d}`);if(h){let{arguments:a,name:i}=h;if(null==a)throw new a_(`missing function_call.arguments for choice ${d}`);if(!i)throw new a_(`missing function_call.name for choice ${d}`);return{...f,message:{content:g,function_call:{arguments:a,name:i},role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}return i?{...f,index:d,finish_reason:c,logprobs:e,message:{...j,role:k,content:g,refusal:b.refusal??null,tool_calls:i.map((b,c)=>{let{function:e,type:f,id:g,...h}=b,{arguments:i,name:j,...k}=e||{};if(null==g)throw new a_(`missing choices[${d}].tool_calls[${c}].id
${cs(a)}`);if(null==f)throw new a_(`missing choices[${d}].tool_calls[${c}].type
${cs(a)}`);if(null==j)throw new a_(`missing choices[${d}].tool_calls[${c}].function.name
${cs(a)}`);if(null==i)throw new a_(`missing choices[${d}].tool_calls[${c}].function.arguments
${cs(a)}`);return{...h,id:g,type:f,function:{...k,name:j,arguments:i}}})}}:{...f,message:{...j,content:g,role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}),created:f,model:g,object:"chat.completion",...h?{system_fingerprint:h}:{}},b&&ck(b)?cj(c,b):{...c,choices:c.choices.map(a=>({...a,message:{...a.message,parsed:null,...a.message.tool_calls?{tool_calls:a.message.tool_calls}:void 0}}))}}(a,aX(this,I,"f"))},R=function(){let a=aX(this,I,"f")?.response_format;return ch(a)?a:null},S=function(a){var b,c,d,e;let f=aX(this,K,"f"),{choices:g,...h}=a;for(let{delta:g,finish_reason:i,index:j,logprobs:k=null,...l}of(f?Object.assign(f,h):f=aW(this,K,{...h,choices:[]},"f"),a.choices)){let a=f.choices[j];if(a||(a=f.choices[j]={finish_reason:i,index:j,message:{},logprobs:k,...l}),k)if(a.logprobs){let{content:d,refusal:e,...f}=k;Object.assign(a.logprobs,f),d&&((b=a.logprobs).content??(b.content=[]),a.logprobs.content.push(...d)),e&&((c=a.logprobs).refusal??(c.refusal=[]),a.logprobs.refusal.push(...e))}else a.logprobs=Object.assign({},k);if(i&&(a.finish_reason=i,aX(this,I,"f")&&ck(aX(this,I,"f")))){if("length"===i)throw new bc;if("content_filter"===i)throw new bd}if(Object.assign(a,l),!g)continue;let{content:h,refusal:m,function_call:n,role:o,tool_calls:p,...q}=g;if(Object.assign(a.message,q),m&&(a.message.refusal=(a.message.refusal||"")+m),o&&(a.message.role=o),n&&(a.message.function_call?(n.name&&(a.message.function_call.name=n.name),n.arguments&&((d=a.message.function_call).arguments??(d.arguments=""),a.message.function_call.arguments+=n.arguments)):a.message.function_call=n),h&&(a.message.content=(a.message.content||"")+h,!a.message.refusal&&aX(this,H,"m",R).call(this)&&(a.message.parsed=cq(a.message.content))),p)for(let{index:b,id:c,type:d,function:f,...g}of(a.message.tool_calls||(a.message.tool_calls=[]),p)){let h=(e=a.message.tool_calls)[b]??(e[b]={});Object.assign(h,g),c&&(h.id=c),d&&(h.type=d),f&&(h.function??(h.function={name:f.name??"",arguments:""})),f?.name&&(h.function.name=f.name),f?.arguments&&(h.function.arguments+=f.arguments,function(a,b){if(!a)return!1;let c=a.tools?.find(a=>a.function?.name===b.function.name);return ci(c)||c?.function.strict||!1}(aX(this,I,"f"),h)&&(h.function.parsed_arguments=cq(h.function.arguments)))}}return f},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("chunk",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new bN(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function cs(a){return JSON.stringify(a)}class ct extends cr{static fromReadableStream(a){let b=new ct(null);return b._run(()=>b._fromReadableStream(a)),b}static runTools(a,b,c){let d=new ct(b),e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}}class cu extends b9{constructor(){super(...arguments),this.messages=new cd(this._client)}create(a,b){return this._client.post("/chat/completions",{body:a,...b,stream:a.stream??!1})}retrieve(a,b){return this._client.get(cc`/chat/completions/${a}`,b)}update(a,b,c){return this._client.post(cc`/chat/completions/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/chat/completions",bX,{query:a,...b})}delete(a,b){return this._client.delete(cc`/chat/completions/${a}`,b)}parse(a,b){for(let b of a.tools??[]){if("function"!==b.type)throw new a_(`Currently only \`function\` tool types support auto-parsing; Received \`${b.type}\``);if(!0!==b.function.strict)throw new a_(`The \`${b.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(a,{...b,headers:{...b?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(b=>cj(b,a))}runTools(a,b){return a.stream?ct.runTools(this._client,a,b):cm.runTools(this._client,a,b)}stream(a,b){return cr.createChatCompletion(this._client,a,b)}}cu.Messages=cd;class cv extends b9{constructor(){super(...arguments),this.completions=new cu(this._client)}}cv.Completions=cu;let cw=Symbol("brand.privateNullableHeaders"),cx=a=>{let b=new Headers,c=new Set;for(let d of a){let a=new Set;for(let[e,f]of function*(a){let b;if(!a)return;if(cw in a){let{values:b,nulls:c}=a;for(let a of(yield*b.entries(),c))yield[a,null];return}let c=!1;for(let d of(a instanceof Headers?b=a.entries():bh(a)?b=a:(c=!0,b=Object.entries(a??{})),b)){let a=d[0];if("string"!=typeof a)throw TypeError("expected header name to be a string");let b=bh(d[1])?d[1]:[d[1]],e=!1;for(let d of b)void 0!==d&&(c&&!e&&(e=!0,yield[a,null]),yield[a,d])}}(d)){let d=e.toLowerCase();a.has(d)||(b.delete(e),a.add(d)),null===f?(b.delete(e),c.add(d)):(b.append(e,f),c.delete(d))}}return{[cw]:!0,values:b,nulls:c}};class cy extends b9{create(a,b){return this._client.post("/audio/speech",{body:a,...b,headers:cx([{Accept:"application/octet-stream"},b?.headers]),__binaryResponse:!0})}}class cz extends b9{create(a,b){return this._client.post("/audio/transcriptions",b0({body:a,...b,stream:a.stream??!1,__metadata:{model:a.model}},this._client))}}class cA extends b9{create(a,b){return this._client.post("/audio/translations",b0({body:a,...b,__metadata:{model:a.model}},this._client))}}class cB extends b9{constructor(){super(...arguments),this.transcriptions=new cz(this._client),this.translations=new cA(this._client),this.speech=new cy(this._client)}}cB.Transcriptions=cz,cB.Translations=cA,cB.Speech=cy;class cC extends b9{create(a,b){return this._client.post("/batches",{body:a,...b})}retrieve(a,b){return this._client.get(cc`/batches/${a}`,b)}list(a={},b){return this._client.getAPIList("/batches",bX,{query:a,...b})}cancel(a,b){return this._client.post(cc`/batches/${a}/cancel`,b)}}class cD extends b9{create(a,b){return this._client.post("/assistants",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cc`/assistants/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cc`/assistants/${a}`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/assistants",bX,{query:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(cc`/assistants/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cE extends b9{create(a,b){return this._client.post("/realtime/sessions",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cF extends b9{create(a,b){return this._client.post("/realtime/transcription_sessions",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cG extends b9{constructor(){super(...arguments),this.sessions=new cE(this._client),this.transcriptionSessions=new cF(this._client)}}cG.Sessions=cE,cG.TranscriptionSessions=cF;class cH extends b9{create(a,b,c){return this._client.post(cc`/threads/${a}/messages`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(cc`/threads/${d}/messages/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cc`/threads/${d}/messages/${a}`,{body:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cc`/threads/${a}/messages`,bX,{query:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{thread_id:d}=b;return this._client.delete(cc`/threads/${d}/messages/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class cI extends b9{retrieve(a,b,c){let{thread_id:d,run_id:e,...f}=b;return this._client.get(cc`/threads/${d}/runs/${e}/steps/${a}`,{query:f,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b,c){let{thread_id:d,...e}=b;return this._client.getAPIList(cc`/threads/${d}/runs/${a}/steps`,bX,{query:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}let cJ=a=>void 0!==globalThis.process?globalThis.process.env?.[a]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(a)?.trim():void 0;class cK extends cg{constructor(){super(...arguments),T.add(this),V.set(this,[]),W.set(this,{}),X.set(this,{}),Y.set(this,void 0),Z.set(this,void 0),$.set(this,void 0),_.set(this,void 0),aa.set(this,void 0),ab.set(this,void 0),ac.set(this,void 0),ad.set(this,void 0),ae.set(this,void 0)}[(V=new WeakMap,W=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,$=new WeakMap,_=new WeakMap,aa=new WeakMap,ab=new WeakMap,ac=new WeakMap,ad=new WeakMap,ae=new WeakMap,T=new WeakSet,Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(a){let b=new U;return b._run(()=>b._fromReadableStream(a)),b}async _fromReadableStream(a,b){let c=b?.signal;c&&(c.aborted&&this.controller.abort(),c.addEventListener("abort",()=>this.controller.abort())),this._connected();let d=bN.fromReadableStream(a,this.controller);for await(let a of d)aX(this,T,"m",af).call(this,a);if(d.controller.signal?.aborted)throw new a1;return this._addRun(aX(this,T,"m",ag).call(this))}toReadableStream(){return new bN(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runToolAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}async _createToolAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.submitToolOutputs(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aX(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new a1;return this._addRun(aX(this,T,"m",ag).call(this))}static createThreadAssistantStream(a,b,c){let d=new U;return d._run(()=>d._threadAssistantStream(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}static createAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}currentEvent(){return aX(this,ac,"f")}currentRun(){return aX(this,ad,"f")}currentMessageSnapshot(){return aX(this,Y,"f")}currentRunStepSnapshot(){return aX(this,ae,"f")}async finalRunSteps(){return await this.done(),Object.values(aX(this,W,"f"))}async finalMessages(){return await this.done(),Object.values(aX(this,X,"f"))}async finalRun(){if(await this.done(),!aX(this,Z,"f"))throw Error("Final run was not received.");return aX(this,Z,"f")}async _createThreadAssistantStream(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));let e={...b,stream:!0},f=await a.createAndRun(e,{...c,signal:this.controller.signal});for await(let a of(this._connected(),f))aX(this,T,"m",af).call(this,a);if(f.controller.signal?.aborted)throw new a1;return this._addRun(aX(this,T,"m",ag).call(this))}async _createAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.create(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aX(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new a1;return this._addRun(aX(this,T,"m",ag).call(this))}static accumulateDelta(a,b){for(let[c,d]of Object.entries(b)){if(!a.hasOwnProperty(c)){a[c]=d;continue}let b=a[c];if(null==b||"index"===c||"type"===c){a[c]=d;continue}if("string"==typeof b&&"string"==typeof d)b+=d;else if("number"==typeof b&&"number"==typeof d)b+=d;else if(bi(b)&&bi(d))b=this.accumulateDelta(b,d);else if(Array.isArray(b)&&Array.isArray(d)){if(b.every(a=>"string"==typeof a||"number"==typeof a)){b.push(...d);continue}for(let a of d){if(!bi(a))throw Error(`Expected array delta entry to be an object but got: ${a}`);let c=a.index;if(null==c)throw console.error(a),Error("Expected array delta entry to have an `index` property");if("number"!=typeof c)throw Error(`Expected array delta entry \`index\` property to be a number but got ${c}`);let d=b[c];null==d?b.push(a):b[c]=this.accumulateDelta(d,a)}continue}else throw Error(`Unhandled record type: ${c}, deltaValue: ${d}, accValue: ${b}`);a[c]=b}return a}_addRun(a){return a}async _threadAssistantStream(a,b,c){return await this._createThreadAssistantStream(b,a,c)}async _runAssistantStream(a,b,c,d){return await this._createAssistantStream(b,a,c,d)}async _runToolAssistantStream(a,b,c,d){return await this._createToolAssistantStream(b,a,c,d)}}U=cK,af=function(a){if(!this.ended)switch(aW(this,ac,a,"f"),aX(this,T,"m",aj).call(this,a),a.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":aX(this,T,"m",an).call(this,a);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aX(this,T,"m",ai).call(this,a);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":aX(this,T,"m",ah).call(this,a);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ag=function(){if(this.ended)throw new a_("stream has ended, this shouldn't happen");if(!aX(this,Z,"f"))throw Error("Final run has not been received");return aX(this,Z,"f")},ah=function(a){let[b,c]=aX(this,T,"m",al).call(this,a,aX(this,Y,"f"));for(let a of(aW(this,Y,b,"f"),aX(this,X,"f")[b.id]=b,c)){let c=b.content[a.index];c?.type=="text"&&this._emit("textCreated",c.text)}switch(a.event){case"thread.message.created":this._emit("messageCreated",a.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",a.data.delta,b),a.data.delta.content)for(let c of a.data.delta.content){if("text"==c.type&&c.text){let a=c.text,d=b.content[c.index];if(d&&"text"==d.type)this._emit("textDelta",a,d.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(c.index!=aX(this,$,"f")){if(aX(this,_,"f"))switch(aX(this,_,"f").type){case"text":this._emit("textDone",aX(this,_,"f").text,aX(this,Y,"f"));break;case"image_file":this._emit("imageFileDone",aX(this,_,"f").image_file,aX(this,Y,"f"))}aW(this,$,c.index,"f")}aW(this,_,b.content[c.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==aX(this,$,"f")){let b=a.data.content[aX(this,$,"f")];if(b)switch(b.type){case"image_file":this._emit("imageFileDone",b.image_file,aX(this,Y,"f"));break;case"text":this._emit("textDone",b.text,aX(this,Y,"f"))}}aX(this,Y,"f")&&this._emit("messageDone",a.data),aW(this,Y,void 0,"f")}},ai=function(a){let b=aX(this,T,"m",ak).call(this,a);switch(aW(this,ae,b,"f"),a.event){case"thread.run.step.created":this._emit("runStepCreated",a.data);break;case"thread.run.step.delta":let c=a.data.delta;if(c.step_details&&"tool_calls"==c.step_details.type&&c.step_details.tool_calls&&"tool_calls"==b.step_details.type)for(let a of c.step_details.tool_calls)a.index==aX(this,aa,"f")?this._emit("toolCallDelta",a,b.step_details.tool_calls[a.index]):(aX(this,ab,"f")&&this._emit("toolCallDone",aX(this,ab,"f")),aW(this,aa,a.index,"f"),aW(this,ab,b.step_details.tool_calls[a.index],"f"),aX(this,ab,"f")&&this._emit("toolCallCreated",aX(this,ab,"f")));this._emit("runStepDelta",a.data.delta,b);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aW(this,ae,void 0,"f"),"tool_calls"==a.data.step_details.type&&aX(this,ab,"f")&&(this._emit("toolCallDone",aX(this,ab,"f")),aW(this,ab,void 0,"f")),this._emit("runStepDone",a.data,b)}},aj=function(a){aX(this,V,"f").push(a),this._emit("event",a)},ak=function(a){switch(a.event){case"thread.run.step.created":return aX(this,W,"f")[a.data.id]=a.data,a.data;case"thread.run.step.delta":let b=aX(this,W,"f")[a.data.id];if(!b)throw Error("Received a RunStepDelta before creation of a snapshot");let c=a.data;if(c.delta){let d=U.accumulateDelta(b,c.delta);aX(this,W,"f")[a.data.id]=d}return aX(this,W,"f")[a.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":aX(this,W,"f")[a.data.id]=a.data}if(aX(this,W,"f")[a.data.id])return aX(this,W,"f")[a.data.id];throw Error("No snapshot available")},al=function(a,b){let c=[];switch(a.event){case"thread.message.created":return[a.data,c];case"thread.message.delta":if(!b)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let d=a.data;if(d.delta.content)for(let a of d.delta.content)if(a.index in b.content){let c=b.content[a.index];b.content[a.index]=aX(this,T,"m",am).call(this,a,c)}else b.content[a.index]=a,c.push(a);return[b,c];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(b)return[b,c];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},am=function(a,b){return U.accumulateDelta(b,a)},an=function(a){switch(aW(this,ad,a.data,"f"),a.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":aW(this,Z,a.data,"f"),aX(this,ab,"f")&&(this._emit("toolCallDone",aX(this,ab,"f")),aW(this,ab,void 0,"f"))}};class cL extends b9{constructor(){super(...arguments),this.steps=new cI(this._client)}create(a,b,c){let{include:d,...e}=b;return this._client.post(cc`/threads/${a}/runs`,{query:{include:d},body:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(cc`/threads/${d}/runs/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cc`/threads/${d}/runs/${a}`,{body:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cc`/threads/${a}/runs`,bX,{query:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{thread_id:d}=b;return this._client.post(cc`/threads/${d}/runs/${a}/cancel`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(d.id,{thread_id:a},c)}createAndStream(a,b,c){return cK.createAssistantStream(a,this._client.beta.threads.runs,b,c)}async poll(a,b,c){let d=cx([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(a,b,{...c,headers:{...c?.headers,...d}}).withResponse();switch(e.status){case"queued":case"in_progress":case"cancelling":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bj(g);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return e}}}stream(a,b,c){return cK.createAssistantStream(a,this._client.beta.threads.runs,b,c)}submitToolOutputs(a,b,c){let{thread_id:d,...e}=b;return this._client.post(cc`/threads/${d}/runs/${a}/submit_tool_outputs`,{body:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}async submitToolOutputsAndPoll(a,b,c){let d=await this.submitToolOutputs(a,b,c);return await this.poll(d.id,b,c)}submitToolOutputsStream(a,b,c){return cK.createToolAssistantStream(a,this._client.beta.threads.runs,b,c)}}cL.Steps=cI;class cM extends b9{constructor(){super(...arguments),this.runs=new cL(this._client),this.messages=new cH(this._client)}create(a={},b){return this._client.post("/threads",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cc`/threads/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cc`/threads/${a}`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b){return this._client.delete(cc`/threads/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}createAndRun(a,b){return this._client.post("/threads/runs",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers]),stream:a.stream??!1})}async createAndRunPoll(a,b){let c=await this.createAndRun(a,b);return await this.runs.poll(c.id,{thread_id:c.thread_id},b)}createAndRunStream(a,b){return cK.createThreadAssistantStream(a,this._client.beta.threads,b)}}cM.Runs=cL,cM.Messages=cH;class cN extends b9{constructor(){super(...arguments),this.realtime=new cG(this._client),this.assistants=new cD(this._client),this.threads=new cM(this._client)}}cN.Realtime=cG,cN.Assistants=cD,cN.Threads=cM;class cO extends b9{create(a,b){return this._client.post("/completions",{body:a,...b,stream:a.stream??!1})}}class cP extends b9{retrieve(a,b,c){let{container_id:d}=b;return this._client.get(cc`/containers/${d}/files/${a}/content`,{...c,headers:cx([{Accept:"application/binary"},c?.headers]),__binaryResponse:!0})}}class cQ extends b9{constructor(){super(...arguments),this.content=new cP(this._client)}create(a,b,c){return this._client.post(cc`/containers/${a}/files`,b0({body:b,...c},this._client))}retrieve(a,b,c){let{container_id:d}=b;return this._client.get(cc`/containers/${d}/files/${a}`,c)}list(a,b={},c){return this._client.getAPIList(cc`/containers/${a}/files`,bX,{query:b,...c})}delete(a,b,c){let{container_id:d}=b;return this._client.delete(cc`/containers/${d}/files/${a}`,{...c,headers:cx([{Accept:"*/*"},c?.headers])})}}cQ.Content=cP;class cR extends b9{constructor(){super(...arguments),this.files=new cQ(this._client)}create(a,b){return this._client.post("/containers",{body:a,...b})}retrieve(a,b){return this._client.get(cc`/containers/${a}`,b)}list(a={},b){return this._client.getAPIList("/containers",bX,{query:a,...b})}delete(a,b){return this._client.delete(cc`/containers/${a}`,{...b,headers:cx([{Accept:"*/*"},b?.headers])})}}cR.Files=cQ;class cS extends b9{create(a,b){let c=!!a.encoding_format,d=c?a.encoding_format:"base64";c&&bL(this._client).debug("embeddings/user defined encoding_format:",a.encoding_format);let e=this._client.post("/embeddings",{body:{...a,encoding_format:d},...b});return c?e:(bL(this._client).debug("embeddings/decoding base64 embeddings from base64"),e._thenUnwrap(a=>(a&&a.data&&a.data.forEach(a=>{let b=a.embedding;a.embedding=(a=>{if("undefined"!=typeof Buffer){let b=Buffer.from(a,"base64");return Array.from(new Float32Array(b.buffer,b.byteOffset,b.length/Float32Array.BYTES_PER_ELEMENT))}{let b=atob(a),c=b.length,d=new Uint8Array(c);for(let a=0;a<c;a++)d[a]=b.charCodeAt(a);return Array.from(new Float32Array(d.buffer))}})(b)}),a)))}}class cT extends b9{retrieve(a,b,c){let{eval_id:d,run_id:e}=b;return this._client.get(cc`/evals/${d}/runs/${e}/output_items/${a}`,c)}list(a,b,c){let{eval_id:d,...e}=b;return this._client.getAPIList(cc`/evals/${d}/runs/${a}/output_items`,bX,{query:e,...c})}}class cU extends b9{constructor(){super(...arguments),this.outputItems=new cT(this._client)}create(a,b,c){return this._client.post(cc`/evals/${a}/runs`,{body:b,...c})}retrieve(a,b,c){let{eval_id:d}=b;return this._client.get(cc`/evals/${d}/runs/${a}`,c)}list(a,b={},c){return this._client.getAPIList(cc`/evals/${a}/runs`,bX,{query:b,...c})}delete(a,b,c){let{eval_id:d}=b;return this._client.delete(cc`/evals/${d}/runs/${a}`,c)}cancel(a,b,c){let{eval_id:d}=b;return this._client.post(cc`/evals/${d}/runs/${a}`,c)}}cU.OutputItems=cT;class cV extends b9{constructor(){super(...arguments),this.runs=new cU(this._client)}create(a,b){return this._client.post("/evals",{body:a,...b})}retrieve(a,b){return this._client.get(cc`/evals/${a}`,b)}update(a,b,c){return this._client.post(cc`/evals/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/evals",bX,{query:a,...b})}delete(a,b){return this._client.delete(cc`/evals/${a}`,b)}}cV.Runs=cU;class cW extends b9{create(a,b){return this._client.post("/files",b0({body:a,...b},this._client))}retrieve(a,b){return this._client.get(cc`/files/${a}`,b)}list(a={},b){return this._client.getAPIList("/files",bX,{query:a,...b})}delete(a,b){return this._client.delete(cc`/files/${a}`,b)}content(a,b){return this._client.get(cc`/files/${a}/content`,{...b,headers:cx([{Accept:"application/binary"},b?.headers]),__binaryResponse:!0})}async waitForProcessing(a,{pollInterval:b=5e3,maxWait:c=18e5}={}){let d=new Set(["processed","error","deleted"]),e=Date.now(),f=await this.retrieve(a);for(;!f.status||!d.has(f.status);)if(await bj(b),f=await this.retrieve(a),Date.now()-e>c)throw new a3({message:`Giving up on waiting for file ${a} to finish processing after ${c} milliseconds.`});return f}}class cX extends b9{}class cY extends b9{run(a,b){return this._client.post("/fine_tuning/alpha/graders/run",{body:a,...b})}validate(a,b){return this._client.post("/fine_tuning/alpha/graders/validate",{body:a,...b})}}class cZ extends b9{constructor(){super(...arguments),this.graders=new cY(this._client)}}cZ.Graders=cY;class c$ extends b9{create(a,b,c){return this._client.getAPIList(cc`/fine_tuning/checkpoints/${a}/permissions`,bW,{body:b,method:"post",...c})}retrieve(a,b={},c){return this._client.get(cc`/fine_tuning/checkpoints/${a}/permissions`,{query:b,...c})}delete(a,b,c){let{fine_tuned_model_checkpoint:d}=b;return this._client.delete(cc`/fine_tuning/checkpoints/${d}/permissions/${a}`,c)}}class c_ extends b9{constructor(){super(...arguments),this.permissions=new c$(this._client)}}c_.Permissions=c$;class c0 extends b9{list(a,b={},c){return this._client.getAPIList(cc`/fine_tuning/jobs/${a}/checkpoints`,bX,{query:b,...c})}}class c1 extends b9{constructor(){super(...arguments),this.checkpoints=new c0(this._client)}create(a,b){return this._client.post("/fine_tuning/jobs",{body:a,...b})}retrieve(a,b){return this._client.get(cc`/fine_tuning/jobs/${a}`,b)}list(a={},b){return this._client.getAPIList("/fine_tuning/jobs",bX,{query:a,...b})}cancel(a,b){return this._client.post(cc`/fine_tuning/jobs/${a}/cancel`,b)}listEvents(a,b={},c){return this._client.getAPIList(cc`/fine_tuning/jobs/${a}/events`,bX,{query:b,...c})}pause(a,b){return this._client.post(cc`/fine_tuning/jobs/${a}/pause`,b)}resume(a,b){return this._client.post(cc`/fine_tuning/jobs/${a}/resume`,b)}}c1.Checkpoints=c0;class c2 extends b9{constructor(){super(...arguments),this.methods=new cX(this._client),this.jobs=new c1(this._client),this.checkpoints=new c_(this._client),this.alpha=new cZ(this._client)}}c2.Methods=cX,c2.Jobs=c1,c2.Checkpoints=c_,c2.Alpha=cZ;class c3 extends b9{}class c4 extends b9{constructor(){super(...arguments),this.graderModels=new c3(this._client)}}c4.GraderModels=c3;class c5 extends b9{createVariation(a,b){return this._client.post("/images/variations",b0({body:a,...b},this._client))}edit(a,b){return this._client.post("/images/edits",b0({body:a,...b,stream:a.stream??!1},this._client))}generate(a,b){return this._client.post("/images/generations",{body:a,...b,stream:a.stream??!1})}}class c6 extends b9{retrieve(a,b){return this._client.get(cc`/models/${a}`,b)}list(a){return this._client.getAPIList("/models",bW,a)}delete(a,b){return this._client.delete(cc`/models/${a}`,b)}}class c7 extends b9{create(a,b){return this._client.post("/moderations",{body:a,...b})}}function c8(a,b){let c=a.output.map(a=>{if("function_call"===a.type)return{...a,parsed_arguments:function(a,b){var c,d;let e=(c=a.tools??[],d=b.name,c.find(a=>"function"===a.type&&a.name===d));return{...b,...b,parsed_arguments:e?.$brand==="auto-parseable-tool"?e.$parseRaw(b.arguments):e?.strict?JSON.parse(b.arguments):null}}(b,a)};if("message"===a.type){let c=a.content.map(a=>{var c,d;return"output_text"===a.type?{...a,parsed:(c=b,d=a.text,c.text?.format?.type!=="json_schema"?null:"$parseRaw"in c.text?.format?(c.text?.format).$parseRaw(d):JSON.parse(d))}:a});return{...a,content:c}}return a}),d=Object.assign({},a,{output:c});return Object.getOwnPropertyDescriptor(a,"output_text")||c9(d),Object.defineProperty(d,"output_parsed",{enumerable:!0,get(){for(let a of d.output)if("message"===a.type){for(let b of a.content)if("output_text"===b.type&&null!==b.parsed)return b.parsed}return null}}),d}function c9(a){let b=[];for(let c of a.output)if("message"===c.type)for(let a of c.content)"output_text"===a.type&&b.push(a.text);a.output_text=b.join("")}class da extends cg{constructor(a){super(),ao.add(this),ap.set(this,void 0),aq.set(this,void 0),ar.set(this,void 0),aW(this,ap,a,"f")}static createResponse(a,b,c){let d=new da(b);return d._run(()=>d._createOrRetrieveResponse(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createOrRetrieveResponse(a,b,c){let d,e=c?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort())),aX(this,ao,"m",as).call(this);let f=null;for await(let e of("response_id"in b?(d=await a.responses.retrieve(b.response_id,{stream:!0},{...c,signal:this.controller.signal,stream:!0}),f=b.starting_after??null):d=await a.responses.create({...b,stream:!0},{...c,signal:this.controller.signal}),this._connected(),d))aX(this,ao,"m",at).call(this,e,f);if(d.controller.signal?.aborted)throw new a1;return aX(this,ao,"m",au).call(this)}[(ap=new WeakMap,aq=new WeakMap,ar=new WeakMap,ao=new WeakSet,as=function(){this.ended||aW(this,aq,void 0,"f")},at=function(a,b){if(this.ended)return;let c=(a,c)=>{(null==b||c.sequence_number>b)&&this._emit(a,c)},d=aX(this,ao,"m",av).call(this,a);switch(c("event",a),a.type){case"response.output_text.delta":{let b=d.output[a.output_index];if(!b)throw new a_(`missing output at index ${a.output_index}`);if("message"===b.type){let d=b.content[a.content_index];if(!d)throw new a_(`missing content at index ${a.content_index}`);if("output_text"!==d.type)throw new a_(`expected content to be 'output_text', got ${d.type}`);c("response.output_text.delta",{...a,snapshot:d.text})}break}case"response.function_call_arguments.delta":{let b=d.output[a.output_index];if(!b)throw new a_(`missing output at index ${a.output_index}`);"function_call"===b.type&&c("response.function_call_arguments.delta",{...a,snapshot:b.arguments});break}default:c(a.type,a)}},au=function(){if(this.ended)throw new a_("stream has ended, this shouldn't happen");let a=aX(this,aq,"f");if(!a)throw new a_("request ended without sending any events");aW(this,aq,void 0,"f");let b=function(a,b){var c;return b&&(c=b,ch(c.text?.format))?c8(a,b):{...a,output_parsed:null,output:a.output.map(a=>"function_call"===a.type?{...a,parsed_arguments:null}:"message"===a.type?{...a,content:a.content.map(a=>({...a,parsed:null}))}:a)}}(a,aX(this,ap,"f"));return aW(this,ar,b,"f"),b},av=function(a){let b=aX(this,aq,"f");if(!b){if("response.created"!==a.type)throw new a_(`When snapshot hasn't been set yet, expected 'response.created' event, got ${a.type}`);return aW(this,aq,a.response,"f")}switch(a.type){case"response.output_item.added":b.output.push(a.item);break;case"response.content_part.added":{let c=b.output[a.output_index];if(!c)throw new a_(`missing output at index ${a.output_index}`);"message"===c.type&&c.content.push(a.part);break}case"response.output_text.delta":{let c=b.output[a.output_index];if(!c)throw new a_(`missing output at index ${a.output_index}`);if("message"===c.type){let b=c.content[a.content_index];if(!b)throw new a_(`missing content at index ${a.content_index}`);if("output_text"!==b.type)throw new a_(`expected content to be 'output_text', got ${b.type}`);b.text+=a.delta}break}case"response.function_call_arguments.delta":{let c=b.output[a.output_index];if(!c)throw new a_(`missing output at index ${a.output_index}`);"function_call"===c.type&&(c.arguments+=a.delta);break}case"response.completed":aW(this,aq,a.response,"f")}return b},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let a=aX(this,ar,"f");if(!a)throw new a_("stream ended without producing a ChatCompletion");return a}}class db extends b9{list(a,b={},c){return this._client.getAPIList(cc`/responses/${a}/input_items`,bX,{query:b,...c})}}class dc extends b9{constructor(){super(...arguments),this.inputItems=new db(this._client)}create(a,b){return this._client.post("/responses",{body:a,...b,stream:a.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&c9(a),a))}retrieve(a,b={},c){return this._client.get(cc`/responses/${a}`,{query:b,...c,stream:b?.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&c9(a),a))}delete(a,b){return this._client.delete(cc`/responses/${a}`,{...b,headers:cx([{Accept:"*/*"},b?.headers])})}parse(a,b){return this._client.responses.create(a,b)._thenUnwrap(b=>c8(b,a))}stream(a,b){return da.createResponse(this._client,a,b)}cancel(a,b){return this._client.post(cc`/responses/${a}/cancel`,b)}}dc.InputItems=db;class dd extends b9{create(a,b,c){return this._client.post(cc`/uploads/${a}/parts`,b0({body:b,...c},this._client))}}class de extends b9{constructor(){super(...arguments),this.parts=new dd(this._client)}create(a,b){return this._client.post("/uploads",{body:a,...b})}cancel(a,b){return this._client.post(cc`/uploads/${a}/cancel`,b)}complete(a,b,c){return this._client.post(cc`/uploads/${a}/complete`,{body:b,...c})}}de.Parts=dd;let df=async a=>{let b=await Promise.allSettled(a),c=b.filter(a=>"rejected"===a.status);if(c.length){for(let a of c)console.error(a.reason);throw Error(`${c.length} promise(s) failed - see the above errors`)}let d=[];for(let a of b)"fulfilled"===a.status&&d.push(a.value);return d};class dg extends b9{create(a,b,c){return this._client.post(cc`/vector_stores/${a}/file_batches`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(cc`/vector_stores/${d}/file_batches/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{vector_store_id:d}=b;return this._client.post(cc`/vector_stores/${d}/file_batches/${a}/cancel`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b);return await this.poll(a,d.id,c)}listFiles(a,b,c){let{vector_store_id:d,...e}=b;return this._client.getAPIList(cc`/vector_stores/${d}/file_batches/${a}/files`,bX,{query:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async poll(a,b,c){let d=cx([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse();switch(e.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bj(g);break;case"failed":case"cancelled":case"completed":return e}}}async uploadAndPoll(a,{files:b,fileIds:c=[]},d){if(null==b||0==b.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let e=Math.min(d?.maxConcurrency??5,b.length),f=this._client,g=b.values(),h=[...c];async function i(a){for(let b of a){let a=await f.files.create({file:b,purpose:"assistants"},d);h.push(a.id)}}let j=Array(e).fill(g).map(i);return await df(j),await this.createAndPoll(a,{file_ids:h})}}class dh extends b9{create(a,b,c){return this._client.post(cc`/vector_stores/${a}/files`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(cc`/vector_stores/${d}/files/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{vector_store_id:d,...e}=b;return this._client.post(cc`/vector_stores/${d}/files/${a}`,{body:e,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(cc`/vector_stores/${a}/files`,bX,{query:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{vector_store_id:d}=b;return this._client.delete(cc`/vector_stores/${d}/files/${a}`,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(a,d.id,c)}async poll(a,b,c){let d=cx([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let e=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse(),f=e.data;switch(f.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=e.response.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await bj(g);break;case"failed":case"completed":return f}}}async upload(a,b,c){let d=await this._client.files.create({file:b,purpose:"assistants"},c);return this.create(a,{file_id:d.id},c)}async uploadAndPoll(a,b,c){let d=await this.upload(a,b,c);return await this.poll(a,d.id,c)}content(a,b,c){let{vector_store_id:d}=b;return this._client.getAPIList(cc`/vector_stores/${d}/files/${a}/content`,bW,{...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class di extends b9{constructor(){super(...arguments),this.files=new dh(this._client),this.fileBatches=new dg(this._client)}create(a,b){return this._client.post("/vector_stores",{body:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(cc`/vector_stores/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(cc`/vector_stores/${a}`,{body:b,...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/vector_stores",bX,{query:a,...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(cc`/vector_stores/${a}`,{...b,headers:cx([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}search(a,b,c){return this._client.getAPIList(cc`/vector_stores/${a}/search`,bW,{body:b,method:"post",...c,headers:cx([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}di.Files=dh,di.FileBatches=dg;class dj extends b9{constructor(){super(...arguments),aw.add(this)}async unwrap(a,b,c=this._client.webhookSecret,d=300){return await this.verifySignature(a,b,c,d),JSON.parse(a)}async verifySignature(a,b,c=this._client.webhookSecret,d=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");aX(this,aw,"m",ax).call(this,c);let e=cx([b]).values,f=aX(this,aw,"m",ay).call(this,e,"webhook-signature"),g=aX(this,aw,"m",ay).call(this,e,"webhook-timestamp"),h=aX(this,aw,"m",ay).call(this,e,"webhook-id"),i=parseInt(g,10);if(isNaN(i))throw new be("Invalid webhook timestamp format");let j=Math.floor(Date.now()/1e3);if(j-i>d)throw new be("Webhook timestamp is too old");if(i>j+d)throw new be("Webhook timestamp is too new");let k=f.split(" ").map(a=>a.startsWith("v1,")?a.substring(3):a),l=c.startsWith("whsec_")?Buffer.from(c.replace("whsec_",""),"base64"):Buffer.from(c,"utf-8"),m=h?`${h}.${g}.${a}`:`${g}.${a}`,n=await crypto.subtle.importKey("raw",l,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let a of k)try{let b=Buffer.from(a,"base64");if(await crypto.subtle.verify("HMAC",n,b,new TextEncoder().encode(m)))return}catch{continue}throw new be("The given webhook signature does not match the expected signature")}}aw=new WeakSet,ax=function(a){if("string"!=typeof a||0===a.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},ay=function(a,b){if(!a)throw Error("Headers are required");let c=a.get(b);if(null==c)throw Error(`Missing required header: ${b}`);return c};class dk{constructor({baseURL:a=cJ("OPENAI_BASE_URL"),apiKey:b=cJ("OPENAI_API_KEY"),organization:c=cJ("OPENAI_ORG_ID")??null,project:d=cJ("OPENAI_PROJECT_ID")??null,webhookSecret:e=cJ("OPENAI_WEBHOOK_SECRET")??null,...f}={}){if(az.add(this),aB.set(this,void 0),this.completions=new cO(this),this.chat=new cv(this),this.embeddings=new cS(this),this.files=new cW(this),this.images=new c5(this),this.audio=new cB(this),this.moderations=new c7(this),this.models=new c6(this),this.fineTuning=new c2(this),this.graders=new c4(this),this.vectorStores=new di(this),this.webhooks=new dj(this),this.beta=new cN(this),this.batches=new cC(this),this.uploads=new de(this),this.responses=new dc(this),this.evals=new cV(this),this.containers=new cR(this),void 0===b)throw new a_("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let g={apiKey:b,organization:c,project:d,webhookSecret:e,...f,baseURL:a||"https://api.openai.com/v1"};if(!g.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new a_("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=g.baseURL,this.timeout=g.timeout??aA.DEFAULT_TIMEOUT,this.logger=g.logger??console;let h="warn";this.logLevel=h,this.logLevel=bG(g.logLevel,"ClientOptions.logLevel",this)??bG(cJ("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??h,this.fetchOptions=g.fetchOptions,this.maxRetries=g.maxRetries??2,this.fetch=g.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),aW(this,aB,br,"f"),this._options=g,this.apiKey=b,this.organization=c,this.project=d,this.webhookSecret=e}withOptions(a){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...a})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:a,nulls:b}){}async authHeaders(a){return cx([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(a){return function(a,b={}){let c,d=a,e=function(a=bA){let b;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.encodeDotInKeys&&"boolean"!=typeof a.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.encoder&&void 0!==a.encoder&&"function"!=typeof a.encoder)throw TypeError("Encoder has to be a function.");let c=a.charset||bA.charset;if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let d=bs;if(void 0!==a.format){if(!bv(bu,a.format))throw TypeError("Unknown format option provided.");d=a.format}let e=bu[d],f=bA.filter;if(("function"==typeof a.filter||bg(a.filter))&&(f=a.filter),b=a.arrayFormat&&a.arrayFormat in by?a.arrayFormat:"indices"in a?a.indices?"indices":"repeat":bA.arrayFormat,"commaRoundTrip"in a&&"boolean"!=typeof a.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let g=void 0===a.allowDots?!0==!!a.encodeDotInKeys||bA.allowDots:!!a.allowDots;return{addQueryPrefix:"boolean"==typeof a.addQueryPrefix?a.addQueryPrefix:bA.addQueryPrefix,allowDots:g,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:bA.allowEmptyArrays,arrayFormat:b,charset:c,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:bA.charsetSentinel,commaRoundTrip:!!a.commaRoundTrip,delimiter:void 0===a.delimiter?bA.delimiter:a.delimiter,encode:"boolean"==typeof a.encode?a.encode:bA.encode,encodeDotInKeys:"boolean"==typeof a.encodeDotInKeys?a.encodeDotInKeys:bA.encodeDotInKeys,encoder:"function"==typeof a.encoder?a.encoder:bA.encoder,encodeValuesOnly:"boolean"==typeof a.encodeValuesOnly?a.encodeValuesOnly:bA.encodeValuesOnly,filter:f,format:d,formatter:e,serializeDate:"function"==typeof a.serializeDate?a.serializeDate:bA.serializeDate,skipNulls:"boolean"==typeof a.skipNulls?a.skipNulls:bA.skipNulls,sort:"function"==typeof a.sort?a.sort:null,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:bA.strictNullHandling}}(b);"function"==typeof e.filter?d=(0,e.filter)("",d):bg(e.filter)&&(c=e.filter);let f=[];if("object"!=typeof d||null===d)return"";let g=by[e.arrayFormat],h="comma"===g&&e.commaRoundTrip;c||(c=Object.keys(d)),e.sort&&c.sort(e.sort);let i=new WeakMap;for(let a=0;a<c.length;++a){let b=c[a];e.skipNulls&&null===d[b]||bz(f,function a(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t,u;let v,w=b,x=s,y=0,z=!1;for(;void 0!==(x=x.get(bB))&&!z;){let a=x.get(b);if(y+=1,void 0!==a)if(a===y)throw RangeError("Cyclic object value");else z=!0;void 0===x.get(bB)&&(y=0)}if("function"==typeof k?w=k(c,w):w instanceof Date?w=n?.(w):"comma"===d&&bg(w)&&(w=bx(w,function(a){return a instanceof Date?n?.(a):a})),null===w){if(g)return j&&!q?j(c,bA.encoder,r,"key",o):c;w=""}if("string"==typeof(t=w)||"number"==typeof t||"boolean"==typeof t||"symbol"==typeof t||"bigint"==typeof t||(u=w)&&"object"==typeof u&&u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u)){if(j){let a=q?c:j(c,bA.encoder,r,"key",o);return[p?.(a)+"="+p?.(j(w,bA.encoder,r,"value",o))]}return[p?.(c)+"="+p?.(String(w))]}let A=[];if(void 0===w)return A;if("comma"===d&&bg(w))q&&j&&(w=bx(w,j)),v=[{value:w.length>0?w.join(",")||null:void 0}];else if(bg(k))v=k;else{let a=Object.keys(w);v=l?a.sort(l):a}let B=i?String(c).replace(/\./g,"%2E"):String(c),C=e&&bg(w)&&1===w.length?B+"[]":B;if(f&&bg(w)&&0===w.length)return C+"[]";for(let c=0;c<v.length;++c){let t=v[c],u="object"==typeof t&&void 0!==t.value?t.value:w[t];if(h&&null===u)continue;let x=m&&i?t.replace(/\./g,"%2E"):t,z=bg(w)?"function"==typeof d?d(C,x):C:C+(m?"."+x:"["+x+"]");s.set(b,y);let B=new WeakMap;B.set(bB,s),bz(A,a(u,z,d,e,f,g,h,i,"comma"===d&&q&&bg(w)?null:j,k,l,m,n,o,p,q,r,B))}return A}(d[b],b,g,h,e.allowEmptyArrays,e.strictNullHandling,e.skipNulls,e.encodeDotInKeys,e.encode?e.encoder:null,e.filter,e.sort,e.allowDots,e.serializeDate,e.format,e.formatter,e.encodeValuesOnly,e.charset,i))}let j=f.join(e.delimiter),k=!0===e.addQueryPrefix?"?":"";return e.charsetSentinel&&("iso-8859-1"===e.charset?k+="utf8=%26%2310003%3B&":k+="utf8=%E2%9C%93&"),j.length>0?k+j:""}(a,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${bk}`}defaultIdempotencyKey(){return`stainless-node-retry-${aY()}`}makeStatusError(a,b,c,d){return a0.generate(a,b,c,d)}buildURL(a,b,c){let d=!aX(this,az,"m",aC).call(this)&&c||this.baseURL,e=new URL(bf.test(a)?a:d+(d.endsWith("/")&&a.startsWith("/")?a.slice(1):a)),f=this.defaultQuery();return!function(a){if(!a)return!0;for(let b in a)return!1;return!0}(f)&&(b={...f,...b}),"object"==typeof b&&b&&!Array.isArray(b)&&(e.search=this.stringifyQuery(b)),e.toString()}async prepareOptions(a){}async prepareRequest(a,{url:b,options:c}){}get(a,b){return this.methodRequest("get",a,b)}post(a,b){return this.methodRequest("post",a,b)}patch(a,b){return this.methodRequest("patch",a,b)}put(a,b){return this.methodRequest("put",a,b)}delete(a,b){return this.methodRequest("delete",a,b)}methodRequest(a,b,c){return this.request(Promise.resolve(c).then(c=>({method:a,path:b,...c})))}request(a,b=null){return new bT(this,this.makeRequest(a,b,void 0))}async makeRequest(a,b,c){let d=await a,e=d.maxRetries??this.maxRetries;null==b&&(b=e),await this.prepareOptions(d);let{req:f,url:g,timeout:h}=await this.buildRequest(d,{retryCount:e-b});await this.prepareRequest(f,{url:g,options:d});let i="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),j=void 0===c?"":`, retryOf: ${c}`,k=Date.now();if(bL(this).debug(`[${i}] sending request`,bM({retryOfRequestLogID:c,method:d.method,url:g,options:d,headers:f.headers})),d.signal?.aborted)throw new a1;let l=new AbortController,m=await this.fetchWithTimeout(g,f,h,l).catch(a$),n=Date.now();if(m instanceof Error){let a=`retrying, ${b} attempts remaining`;if(d.signal?.aborted)throw new a1;let e=aZ(m)||/timed? ?out/i.test(String(m)+("cause"in m?String(m.cause):""));if(b)return bL(this).info(`[${i}] connection ${e?"timed out":"failed"} - ${a}`),bL(this).debug(`[${i}] connection ${e?"timed out":"failed"} (${a})`,bM({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),this.retryRequest(d,b,c??i);if(bL(this).info(`[${i}] connection ${e?"timed out":"failed"} - error; no more retries left`),bL(this).debug(`[${i}] connection ${e?"timed out":"failed"} (error; no more retries left)`,bM({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),e)throw new a3;throw new a2({cause:m})}let o=[...m.headers.entries()].filter(([a])=>"x-request-id"===a).map(([a,b])=>", "+a+": "+JSON.stringify(b)).join(""),p=`[${i}${j}${o}] ${f.method} ${g} ${m.ok?"succeeded":"failed"} with status ${m.status} in ${n-k}ms`;if(!m.ok){let a=await this.shouldRetry(m);if(b&&a){let a=`retrying, ${b} attempts remaining`;return await bq(m.body),bL(this).info(`${p} - ${a}`),bL(this).debug(`[${i}] response error (${a})`,bM({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),this.retryRequest(d,b,c??i,m.headers)}let e=a?"error; no more retries left":"error; not retryable";bL(this).info(`${p} - ${e}`);let f=await m.text().catch(a=>a$(a).message),g=(a=>{try{return JSON.parse(a)}catch(a){return}})(f),h=g?void 0:f;throw bL(this).debug(`[${i}] response error (${e})`,bM({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,message:h,durationMs:Date.now()-k})),this.makeStatusError(m.status,g,h,m.headers)}return bL(this).info(p),bL(this).debug(`[${i}] response start`,bM({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),{response:m,options:d,controller:l,requestLogID:i,retryOfRequestLogID:c,startTime:k}}getAPIList(a,b,c){return this.requestAPIList(b,{method:"get",path:a,...c})}requestAPIList(a,b){return new bV(this,this.makeRequest(b,null,void 0),a)}async fetchWithTimeout(a,b,c,d){let{signal:e,method:f,...g}=b||{};e&&e.addEventListener("abort",()=>d.abort());let h=setTimeout(()=>d.abort(),c),i=globalThis.ReadableStream&&g.body instanceof globalThis.ReadableStream||"object"==typeof g.body&&null!==g.body&&Symbol.asyncIterator in g.body,j={signal:d.signal,...i?{duplex:"half"}:{},method:"GET",...g};f&&(j.method=f.toUpperCase());try{return await this.fetch.call(void 0,a,j)}finally{clearTimeout(h)}}async shouldRetry(a){let b=a.headers.get("x-should-retry");return"true"===b||"false"!==b&&(408===a.status||409===a.status||429===a.status||!!(a.status>=500))}async retryRequest(a,b,c,d){let e,f=d?.get("retry-after-ms");if(f){let a=parseFloat(f);Number.isNaN(a)||(e=a)}let g=d?.get("retry-after");if(g&&!e){let a=parseFloat(g);e=Number.isNaN(a)?Date.parse(g)-Date.now():1e3*a}if(!(e&&0<=e&&e<6e4)){let c=a.maxRetries??this.maxRetries;e=this.calculateDefaultRetryTimeoutMillis(b,c)}return await bj(e),this.makeRequest(a,b-1,c)}calculateDefaultRetryTimeoutMillis(a,b){return Math.min(.5*Math.pow(2,b-a),8)*(1-.25*Math.random())*1e3}async buildRequest(a,{retryCount:b=0}={}){let c={...a},{method:d,path:e,query:f,defaultBaseURL:g}=c,h=this.buildURL(e,f,g);"timeout"in c&&((a,b)=>{if("number"!=typeof b||!Number.isInteger(b))throw new a_(`${a} must be an integer`);if(b<0)throw new a_(`${a} must be a positive integer`)})("timeout",c.timeout),c.timeout=c.timeout??this.timeout;let{bodyHeaders:i,body:j}=this.buildBody({options:c}),k=await this.buildHeaders({options:a,method:d,bodyHeaders:i,retryCount:b});return{req:{method:d,headers:k,...c.signal&&{signal:c.signal},...globalThis.ReadableStream&&j instanceof globalThis.ReadableStream&&{duplex:"half"},...j&&{body:j},...this.fetchOptions??{},...c.fetchOptions??{}},url:h,timeout:c.timeout}}async buildHeaders({options:a,method:b,bodyHeaders:c,retryCount:e}){let f={};this.idempotencyHeader&&"get"!==b&&(a.idempotencyKey||(a.idempotencyKey=this.defaultIdempotencyKey()),f[this.idempotencyHeader]=a.idempotencyKey);let g=cx([f,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(e),...a.timeout?{"X-Stainless-Timeout":String(Math.trunc(a.timeout/1e3))}:{},...d??(d=(()=>{let a="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bk,"X-Stainless-OS":bm(Deno.build.os),"X-Stainless-Arch":bl(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bk,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bk,"X-Stainless-OS":bm(globalThis.process.platform??"unknown"),"X-Stainless-Arch":bl(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let b=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:a,pattern:b}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let c=b.exec(navigator.userAgent);if(c){let b=c[1]||0,d=c[2]||0,e=c[3]||0;return{browser:a,version:`${b}.${d}.${e}`}}}return null}();return b?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bk,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${b.browser}`,"X-Stainless-Runtime-Version":b.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":bk,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(a),this._options.defaultHeaders,c,a.headers]);return this.validateHeaders(g),g.values}buildBody({options:{body:a,headers:b}}){if(!a)return{bodyHeaders:void 0,body:void 0};let c=cx([b]);return ArrayBuffer.isView(a)||a instanceof ArrayBuffer||a instanceof DataView||"string"==typeof a&&c.values.has("content-type")||a instanceof Blob||a instanceof FormData||a instanceof URLSearchParams||globalThis.ReadableStream&&a instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:a}:"object"==typeof a&&(Symbol.asyncIterator in a||Symbol.iterator in a&&"next"in a&&"function"==typeof a.next)?{bodyHeaders:void 0,body:bo(a)}:aX(this,aB,"f").call(this,{body:a,headers:c})}}aA=dk,aB=new WeakMap,az=new WeakSet,aC=function(){return"https://api.openai.com/v1"!==this.baseURL},dk.OpenAI=aA,dk.DEFAULT_TIMEOUT=6e5,dk.OpenAIError=a_,dk.APIError=a0,dk.APIConnectionError=a2,dk.APIConnectionTimeoutError=a3,dk.APIUserAbortError=a1,dk.NotFoundError=a7,dk.ConflictError=a8,dk.RateLimitError=ba,dk.BadRequestError=a4,dk.AuthenticationError=a5,dk.InternalServerError=bb,dk.PermissionDeniedError=a6,dk.UnprocessableEntityError=a9,dk.InvalidWebhookSignatureError=be,dk.toFile=b7,dk.Completions=cO,dk.Chat=cv,dk.Embeddings=cS,dk.Files=cW,dk.Images=c5,dk.Audio=cB,dk.Moderations=c7,dk.Models=c6,dk.FineTuning=c2,dk.Graders=c4,dk.VectorStores=di,dk.Webhooks=dj,dk.Beta=cN,dk.Batches=cC,dk.Uploads=de,dk.Responses=dc,dk.Evals=cV,dk.Containers=cR;var dl=c(9288),dm=c.n(dl);let dn=new dk({apiKey:process.env.OPENAI_API_KEY});async function dp(a){try{let b=await (0,aV.U)(),{data:{user:c},error:d}=await b.auth.getUser();if(d||!c)return aU.NextResponse.json({error:"Unauthorized"},{status:401});let{imageData:e,prompt:f}=await a.json();if(!e||!f)return aU.NextResponse.json({error:"Image data and prompt are required"},{status:400});if(f.length>32e3)return aU.NextResponse.json({error:"Prompt too long. Maximum 32000 characters for gpt-image-1."},{status:400});let g=e.replace(/^data:image\/[a-z]+;base64,/,""),h=Buffer.from(g,"base64"),i=await dm()(h).png().resize(1024,1024,{fit:"inside",withoutEnlargement:!0,background:{r:255,g:255,b:255,alpha:1}}).toBuffer(),j=new Blob([i],{type:"image/png"}),k=new File([j],"image.png",{type:"image/png"});console.log("Sending image to OpenAI:",{originalSize:h.length,processedSize:i.length,prompt:f,fileType:k.type}),console.log("Using gpt-image-1 for image transformation...");let l=await dn.images.edit({model:"gpt-image-1",image:k,prompt:f,n:1,size:"1024x1024",output_format:"png",input_fidelity:"high",quality:"high"}),m="gpt-image-1";console.log("Model used:",m),console.log("Response data length:",l.data?.length);let n=null;if(l.data&&Array.isArray(l.data)&&l.data.length>0){let a=l.data[0];a.b64_json&&(n=`data:image/png;base64,${a.b64_json}`,console.log("Generated data URL from base64 for gpt-image-1"))}if(!n)throw console.error("No image data found in OpenAI response. Full response:",JSON.stringify(l,null,2)),Error(`No edited image data found. Model: ${m}, Response keys: ${l.data?.[0]?Object.keys(l.data[0]).join(", "):"no data"}`);return aU.NextResponse.json({success:!0,editedImageUrl:n,originalPrompt:f,processedAt:new Date().toISOString(),model:m})}catch(a){if(console.error("Processing error:",a),a instanceof dk.APIError){if(401===a.status)return aU.NextResponse.json({error:"Invalid API key configuration"},{status:500});if(429===a.status)return aU.NextResponse.json({error:"API rate limit exceeded. Please try again later."},{status:429});if(400===a.status)return aU.NextResponse.json({error:"Invalid image or prompt. Please check your input and try again."},{status:400});return aU.NextResponse.json({error:`API error: ${a.message}`},{status:a.status||500})}return aU.NextResponse.json({error:"Processing failed. Please try again."},{status:500})}}let dq=new aE.AppRouteRouteModule({definition:{kind:aF.RouteKind.APP_ROUTE,page:"/api/process/route",pathname:"/api/process",filename:"route",bundlePath:"app/api/process/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/api/process/route.ts",nextConfigOutput:"",userland:aD}),{workAsyncStorage:dr,workUnitAsyncStorage:ds,serverHooks:dt}=dq;function du(){return(0,aG.patchFetch)({workAsyncStorage:dr,workUnitAsyncStorage:ds})}async function dv(a,b,c){var d;let e="/api/process/route";"/index"===e&&(e="/");let f=await dq.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:i,isDraftMode:j,prerenderManifest:k,routerServerContext:l,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,resolvedPathname:o}=f,p=(0,aJ.normalizeAppPath)(e),q=!!(k.dynamicRoutes[p]||k.routes[o]);if(q&&!j){let a=!!k.routes[o],b=k.dynamicRoutes[p];if(b&&!1===b.fallback&&!a)throw new aS.NoFallbackError}let r=null;!q||dq.isDev||j||(r="/index"===(r=o)?"/":r);let s=!0===dq.isDev||!q,t=q&&!s,u=a.method||"GET",v=(0,aI.getTracer)(),w=v.getActiveScopeSpan(),x={params:h,prerenderManifest:k,renderOpts:{experimental:{dynamicIO:!!i.experimental.dynamicIO,authInterrupts:!!i.experimental.authInterrupts},supportsDynamicResponse:s,incrementalCache:(0,aH.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=i.experimental)?void 0:d.cacheLife,isRevalidate:t,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>dq.onRequestError(a,b,d,l)},sharedContext:{buildId:g}},y=new aK.NodeNextRequest(a),z=new aK.NodeNextResponse(b),A=aL.NextRequestAdapter.fromNodeNextRequest(y,(0,aL.signalFromNodeResponse)(b));try{let d=async c=>dq.handle(A,x).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=v.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==aM.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${u} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${u} ${a.url}`)}),f=async f=>{var g,h;let o=async({previousCacheEntry:g})=>{try{if(!(0,aH.getRequestMeta)(a,"minimalMode")&&m&&n&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=x.renderOpts.fetchMetrics;let h=x.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=x.renderOpts.collectedTags;if(!q)return await (0,aO.I)(y,z,e,x.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,aP.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[aR.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==x.renderOpts.collectedRevalidate&&!(x.renderOpts.collectedRevalidate>=aR.INFINITE_CACHE)&&x.renderOpts.collectedRevalidate,d=void 0===x.renderOpts.collectedExpire||x.renderOpts.collectedExpire>=aR.INFINITE_CACHE?void 0:x.renderOpts.collectedExpire;return{value:{kind:aT.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await dq.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,aN.c)({isRevalidate:t,isOnDemandRevalidate:m})},l),b}},p=await dq.handleResponse({req:a,nextConfig:i,cacheKey:r,routeKind:aF.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,responseGenerator:o,waitUntil:c.waitUntil});if(!q)return null;if((null==p||null==(g=p.value)?void 0:g.kind)!==aT.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==p||null==(h=p.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,aH.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",m?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),j&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let s=(0,aP.fromNodeOutgoingHttpHeaders)(p.value.headers);return(0,aH.getRequestMeta)(a,"minimalMode")&&q||s.delete(aR.NEXT_CACHE_TAGS_HEADER),!p.cacheControl||b.getHeader("Cache-Control")||s.get("Cache-Control")||s.set("Cache-Control",(0,aQ.getCacheControlHeader)(p.cacheControl)),await (0,aO.I)(y,z,new Response(p.value.body,{headers:s,status:p.value.status||200})),null};w?await f(w):await v.withPropagatedContext(a.headers,()=>v.trace(aM.BaseServerSpan.handleRequest,{spanName:`${u} ${a.url}`,kind:aI.SpanKind.SERVER,attributes:{"http.method":u,"http.target":a.url}},f))}catch(b){if(w||await dq.onRequestError(a,b,{routerKind:"App Router",routePath:p,routeType:"route",revalidateReason:(0,aN.c)({isRevalidate:t,isOnDemandRevalidate:m})}),q)throw b;return await (0,aO.I)(y,z,new Response(null,{status:500})),null}}},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,866,55],()=>b(b.s=77940));module.exports=c})();