1:"$Sreact.fragment"
2:I[1362,["177","static/chunks/app/layout-5cd6870e9d77b1c1.js"],"ThemeProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[3047,["352","static/chunks/352-b43a1550bb8efb38.js","30","static/chunks/30-5b23c1059f79074e.js","11","static/chunks/11-17a56a0d2db871dd.js","794","static/chunks/app/auth/forgot-password/page-33b6731de3d6d5db.js"],"ForgotPasswordForm"]
6:I[9665,[],"OutletBoundary"]
8:I[4911,[],"AsyncMetadataOutlet"]
a:I[9665,[],"ViewportBoundary"]
c:I[9665,[],"MetadataBoundary"]
d:"$Sreact.suspense"
f:I[8393,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/abe0129b58fd8378.css","style"]
0:{"P":null,"b":"PtwKkgM79u0xudCM-Wh7e","p":"","c":["","auth","forgot-password"],"i":false,"f":[[["",{"children":["auth",{"children":["forgot-password",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/abe0129b58fd8378.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__className_5cfdac antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["auth",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["forgot-password",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"flex min-h-svh w-full items-center justify-center p-6 md:p-10","children":["$","div",null,{"className":"w-full max-w-sm","children":["$","$L5",null,{}]}]}],null,["$","$L6",null,{"children":["$L7",["$","$L8",null,{"promise":"$@9"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$La",null,{"children":"$Lb"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$Lc",null,{"children":["$","div",null,{"hidden":true,"children":["$","$d",null,{"fallback":null,"children":"$Le"}]}]}]]}],false]],"m":"$undefined","G":["$f",[]],"s":false,"S":true}
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
7:null
10:I[8175,[],"IconMark"]
9:{"metadata":[["$","title","0",{"children":"Next.js and Supabase Starter Kit"}],["$","meta","1",{"name":"description","content":"The fastest way to build apps with Next.js and Supabase"}],["$","meta","2",{"property":"og:title","content":"Next.js and Supabase Starter Kit"}],["$","meta","3",{"property":"og:description","content":"The fastest way to build apps with Next.js and Supabase"}],["$","meta","4",{"property":"og:image:type","content":"image/png"}],["$","meta","5",{"property":"og:image:width","content":"1200"}],["$","meta","6",{"property":"og:image:height","content":"600"}],["$","meta","7",{"property":"og:image","content":"http://localhost:3000/opengraph-image.png?2e0bc232e210f89d"}],["$","meta","8",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","9",{"name":"twitter:title","content":"Next.js and Supabase Starter Kit"}],["$","meta","10",{"name":"twitter:description","content":"The fastest way to build apps with Next.js and Supabase"}],["$","meta","11",{"name":"twitter:image:type","content":"image/png"}],["$","meta","12",{"name":"twitter:image:width","content":"1200"}],["$","meta","13",{"name":"twitter:image:height","content":"600"}],["$","meta","14",{"name":"twitter:image","content":"http://localhost:3000/twitter-image.png?2e0bc232e210f89d"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L10","16",{}]],"error":null,"digest":"$undefined"}
e:"$9:metadata"
