(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{3:(a,b,c)=>{"use strict";c.r(b),c.d(b,{Headers:()=>g,Request:()=>h,Response:()=>i,default:()=>f,fetch:()=>e});var d=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==c.g)return c.g;throw Error("unable to locate global object")}();let e=d.fetch,f=d.fetch.bind(d),g=d.Headers,h=d.Request,i=d.Response},17:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>dI});var e,f,g,h,i,j,k,l,m,n,o,p={};async function q(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}c.r(p),c.d(p,{config:()=>dE,middleware:()=>dD});let r=null;async function s(){if("phase-production-build"===process.env.NEXT_PHASE)return;r||(r=q());let a=await r;if(null==a?void 0:a.register)try{await a.register()}catch(a){throw a.message=`An error occurred while loading instrumentation hook: ${a.message}`,a}}async function t(...a){let b=await q();try{var c;await (null==b||null==(c=b.onRequestError)?void 0:c.call(b,...a))}catch(a){console.error("Error in instrumentation.onRequestError:",a)}}let u=null;function v(){return u||(u=s()),u}function w(a){return`The edge runtime does not support Node.js '${a}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==c.g.process&&(process.env=c.g.process.env,c.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(a){let b=new Proxy(function(){},{get(b,c){if("then"===c)return{};throw Object.defineProperty(Error(w(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(w(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(c,d,e){if("function"==typeof e[0])return e[0](b);throw Object.defineProperty(Error(w(a)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>b})},enumerable:!1,configurable:!1}),v();class x extends Error{constructor({page:a}){super(`The middleware "${a}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class y extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class z extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let A="_N_T_",B={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function C(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function D(a){let b={},c=[];if(a)for(let[d,e]of a.entries())"set-cookie"===d.toLowerCase()?(c.push(...C(e)),b[d]=1===c.length?c[0]:c):b[d]=e;return b}function E(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...B,GROUP:{builtinReact:[B.reactServerComponents,B.actionBrowser],serverOnly:[B.reactServerComponents,B.actionBrowser,B.instrument,B.middleware],neutralTarget:[B.apiNode,B.apiEdge],clientOnly:[B.serverSideRendering,B.appPagesBrowser],bundled:[B.reactServerComponents,B.actionBrowser,B.serverSideRendering,B.appPagesBrowser,B.shared,B.instrument,B.middleware],appPages:[B.reactServerComponents,B.serverSideRendering,B.appPagesBrowser,B.actionBrowser]}});let F=Symbol("response"),G=Symbol("passThrough"),H=Symbol("waitUntil");class I{constructor(a,b){this[G]=!1,this[H]=b?{kind:"external",function:b}:{kind:"internal",promises:[]}}respondWith(a){this[F]||(this[F]=Promise.resolve(a))}passThroughOnException(){this[G]=!0}waitUntil(a){if("external"===this[H].kind)return(0,this[H].function)(a);this[H].promises.push(a)}}class J extends I{constructor(a){var b;super(a.request,null==(b=a.context)?void 0:b.waitUntil),this.sourcePage=a.page}get request(){throw Object.defineProperty(new x({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new x({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function K(a){return a.replace(/\/$/,"")||"/"}function L(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}function M(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=L(a);return""+b+c+d+e}function N(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:d,hash:e}=L(a);return""+c+b+d+e}function O(a,b){if("string"!=typeof a)return!1;let{pathname:c}=L(a);return c===b||c.startsWith(b+"/")}let P=new WeakMap;function Q(a,b){let c;if(!b)return{pathname:a};let d=P.get(b);d||(d=b.map(a=>a.toLowerCase()),P.set(b,d));let e=a.split("/",2);if(!e[1])return{pathname:a};let f=e[1].toLowerCase(),g=d.indexOf(f);return g<0?{pathname:a}:(c=b[g],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}let R=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function S(a,b){return new URL(String(a).replace(R,"localhost"),b&&String(b).replace(R,"localhost"))}let T=Symbol("NextURLInternal");class U{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[T]={url:S(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,d,e;let f=function(a,b){var c,d;let{basePath:e,i18n:f,trailingSlash:g}=null!=(c=b.nextConfig)?c:{},h={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):g};e&&O(h.pathname,e)&&(h.pathname=function(a,b){if(!O(a,b))return a;let c=a.slice(b.length);return c.startsWith("/")?c:"/"+c}(h.pathname,e),h.basePath=e);let i=h.pathname;if(h.pathname.startsWith("/_next/data/")&&h.pathname.endsWith(".json")){let a=h.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");h.buildId=a[0],i="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(h.pathname=i)}if(f){let a=b.i18nProvider?b.i18nProvider.analyze(h.pathname):Q(h.pathname,f.locales);h.locale=a.detectedLocale,h.pathname=null!=(d=a.pathname)?d:h.pathname,!a.detectedLocale&&h.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(i):Q(i,f.locales)).detectedLocale&&(h.locale=a.detectedLocale)}return h}(this[T].url.pathname,{nextConfig:this[T].options.nextConfig,parseData:!0,i18nProvider:this[T].options.i18nProvider}),g=function(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}(this[T].url,this[T].options.headers);this[T].domainLocale=this[T].options.i18nProvider?this[T].options.i18nProvider.detectDomainLocale(g):function(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}(null==(b=this[T].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,g);let h=(null==(c=this[T].domainLocale)?void 0:c.defaultLocale)||(null==(e=this[T].options.nextConfig)||null==(d=e.i18n)?void 0:d.defaultLocale);this[T].url.pathname=f.pathname,this[T].defaultLocale=h,this[T].basePath=f.basePath??"",this[T].buildId=f.buildId,this[T].locale=f.locale??h,this[T].trailingSlash=f.trailingSlash}formatPathname(){var a;let b;return b=function(a,b,c,d){if(!b||b===c)return a;let e=a.toLowerCase();return!d&&(O(e,"/api")||O(e,"/"+b.toLowerCase()))?a:M(a,"/"+b)}((a={basePath:this[T].basePath,buildId:this[T].buildId,defaultLocale:this[T].options.forceLocale?void 0:this[T].defaultLocale,locale:this[T].locale,pathname:this[T].url.pathname,trailingSlash:this[T].trailingSlash}).pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix),(a.buildId||!a.trailingSlash)&&(b=K(b)),a.buildId&&(b=N(M(b,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),b=M(b,a.basePath),!a.buildId&&a.trailingSlash?b.endsWith("/")?b:N(b,"/"):K(b)}formatSearch(){return this[T].url.search}get buildId(){return this[T].buildId}set buildId(a){this[T].buildId=a}get locale(){return this[T].locale??""}set locale(a){var b,c;if(!this[T].locale||!(null==(c=this[T].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[T].locale=a}get defaultLocale(){return this[T].defaultLocale}get domainLocale(){return this[T].domainLocale}get searchParams(){return this[T].url.searchParams}get host(){return this[T].url.host}set host(a){this[T].url.host=a}get hostname(){return this[T].url.hostname}set hostname(a){this[T].url.hostname=a}get port(){return this[T].url.port}set port(a){this[T].url.port=a}get protocol(){return this[T].url.protocol}set protocol(a){this[T].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[T].url=S(a),this.analyze()}get origin(){return this[T].url.origin}get pathname(){return this[T].url.pathname}set pathname(a){this[T].url.pathname=a}get hash(){return this[T].url.hash}set hash(a){this[T].url.hash=a}get search(){return this[T].url.search}set search(a){this[T].url.search=a}get password(){return this[T].url.password}set password(a){this[T].url.password=a}get username(){return this[T].url.username}set username(a){this[T].url.username=a}get basePath(){return this[T].basePath}set basePath(a){this[T].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new U(String(this),this[T].options)}}var V=c(724);let W=Symbol("internal request");class X extends Request{constructor(a,b={}){let c="string"!=typeof a&&"url"in a?a.url:String(a);E(c),a instanceof Request?super(a,b):super(c,b);let d=new U(c,{headers:D(this.headers),nextConfig:b.nextConfig});this[W]={cookies:new V.RequestCookies(this.headers),nextUrl:d,url:d.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[W].cookies}get nextUrl(){return this[W].nextUrl}get page(){throw new y}get ua(){throw new z}get url(){return this[W].url}}class Y{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}let Z=Symbol("internal response"),$=new Set([301,302,303,307,308]);function _(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class aa extends Response{constructor(a,b={}){super(a,b);let c=this.headers,d=new Proxy(new V.ResponseCookies(c),{get(a,d,e){switch(d){case"delete":case"set":return(...e)=>{let f=Reflect.apply(a[d],a,e),g=new Headers(c);return f instanceof V.ResponseCookies&&c.set("x-middleware-set-cookie",f.getAll().map(a=>(0,V.stringifyCookie)(a)).join(",")),_(b,g),f};default:return Y.get(a,d,e)}}});this[Z]={cookies:d,url:b.url?new U(b.url,{headers:D(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[Z].cookies}static json(a,b){let c=Response.json(a,b);return new aa(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!$.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",E(a)),new aa(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",E(a)),_(b,c),new aa(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),_(a,b),new aa(null,{...a,headers:b})}}function ab(a,b){let c="string"==typeof b?new URL(b):b,d=new URL(a,b),e=d.origin===c.origin;return{url:e?d.toString().slice(c.origin.length):d.toString(),isRelative:e}}let ac="Next-Router-Prefetch",ad=["RSC","Next-Router-State-Tree",ac,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"];class ae extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ae}}class af extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,c,d){if("symbol"==typeof c)return Y.get(b,c,d);let e=c.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);if(void 0!==f)return Y.get(b,f,d)},set(b,c,d,e){if("symbol"==typeof c)return Y.set(b,c,d,e);let f=c.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);return Y.set(b,g??c,d,e)},has(b,c){if("symbol"==typeof c)return Y.has(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0!==e&&Y.has(b,e)},deleteProperty(b,c){if("symbol"==typeof c)return Y.deleteProperty(b,c);let d=c.toLowerCase(),e=Object.keys(a).find(a=>a.toLowerCase()===d);return void 0===e||Y.deleteProperty(b,e)}})}static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"append":case"delete":case"set":return ae.callable;default:return Y.get(a,b,c)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new af(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}let ag=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class ah{disable(){throw ag}getStore(){}run(){throw ag}exit(){throw ag}enterWith(){throw ag}static bind(a){return a}}let ai="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function aj(){return ai?new ai:new ah}let ak=aj(),al=aj();class am extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new am}}class an{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return am.callable;default:return Y.get(a,b,c)}}})}}let ao=Symbol.for("next.mutated.cookies");class ap{static wrap(a,b){let c=new V.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let d=[],e=new Set,f=()=>{let a=ak.getStore();if(a&&(a.pathWasRevalidated=!0),d=c.getAll().filter(a=>e.has(a.name)),b){let a=[];for(let b of d){let c=new V.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},g=new Proxy(c,{get(a,b,c){switch(b){case ao:return d;case"delete":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),g}finally{f()}};case"set":return function(...b){e.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),g}finally{f()}};default:return Y.get(a,b,c)}}});return g}}function aq(a){if("action"!==function(a){let b=al.getStore();switch(!b&&function(a){throw Object.defineProperty(Error(`\`${a}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(a),b.type){case"request":default:return b;case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${a}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(a).phase)throw new am}var ar=function(a){return a.handleRequest="BaseServer.handleRequest",a.run="BaseServer.run",a.pipe="BaseServer.pipe",a.getStaticHTML="BaseServer.getStaticHTML",a.render="BaseServer.render",a.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",a.renderToResponse="BaseServer.renderToResponse",a.renderToHTML="BaseServer.renderToHTML",a.renderError="BaseServer.renderError",a.renderErrorToResponse="BaseServer.renderErrorToResponse",a.renderErrorToHTML="BaseServer.renderErrorToHTML",a.render404="BaseServer.render404",a}(ar||{}),as=function(a){return a.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",a.loadComponents="LoadComponents.loadComponents",a}(as||{}),at=function(a){return a.getRequestHandler="NextServer.getRequestHandler",a.getServer="NextServer.getServer",a.getServerRequestHandler="NextServer.getServerRequestHandler",a.createServer="createServer.createServer",a}(at||{}),au=function(a){return a.compression="NextNodeServer.compression",a.getBuildId="NextNodeServer.getBuildId",a.createComponentTree="NextNodeServer.createComponentTree",a.clientComponentLoading="NextNodeServer.clientComponentLoading",a.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",a.generateStaticRoutes="NextNodeServer.generateStaticRoutes",a.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",a.generatePublicRoutes="NextNodeServer.generatePublicRoutes",a.generateImageRoutes="NextNodeServer.generateImageRoutes.route",a.sendRenderResult="NextNodeServer.sendRenderResult",a.proxyRequest="NextNodeServer.proxyRequest",a.runApi="NextNodeServer.runApi",a.render="NextNodeServer.render",a.renderHTML="NextNodeServer.renderHTML",a.imageOptimizer="NextNodeServer.imageOptimizer",a.getPagePath="NextNodeServer.getPagePath",a.getRoutesManifest="NextNodeServer.getRoutesManifest",a.findPageComponents="NextNodeServer.findPageComponents",a.getFontManifest="NextNodeServer.getFontManifest",a.getServerComponentManifest="NextNodeServer.getServerComponentManifest",a.getRequestHandler="NextNodeServer.getRequestHandler",a.renderToHTML="NextNodeServer.renderToHTML",a.renderError="NextNodeServer.renderError",a.renderErrorToHTML="NextNodeServer.renderErrorToHTML",a.render404="NextNodeServer.render404",a.startResponse="NextNodeServer.startResponse",a.route="route",a.onProxyReq="onProxyReq",a.apiResolver="apiResolver",a.internalFetch="internalFetch",a}(au||{}),av=function(a){return a.startServer="startServer.startServer",a}(av||{}),aw=function(a){return a.getServerSideProps="Render.getServerSideProps",a.getStaticProps="Render.getStaticProps",a.renderToString="Render.renderToString",a.renderDocument="Render.renderDocument",a.createBodyResult="Render.createBodyResult",a}(aw||{}),ax=function(a){return a.renderToString="AppRender.renderToString",a.renderToReadableStream="AppRender.renderToReadableStream",a.getBodyResult="AppRender.getBodyResult",a.fetch="AppRender.fetch",a}(ax||{}),ay=function(a){return a.executeRoute="Router.executeRoute",a}(ay||{}),az=function(a){return a.runHandler="Node.runHandler",a}(az||{}),aA=function(a){return a.runHandler="AppRouteRouteHandlers.runHandler",a}(aA||{}),aB=function(a){return a.generateMetadata="ResolveMetadata.generateMetadata",a.generateViewport="ResolveMetadata.generateViewport",a}(aB||{}),aC=function(a){return a.execute="Middleware.execute",a}(aC||{});let aD=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],aE=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function aF(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}let{context:aG,propagation:aH,trace:aI,SpanStatusCode:aJ,SpanKind:aK,ROOT_CONTEXT:aL}=d=c(956);class aM extends Error{constructor(a,b){super(),this.bubble=a,this.result=b}}let aN=(a,b)=>{(function(a){return"object"==typeof a&&null!==a&&a instanceof aM})(b)&&b.bubble?a.setAttribute("next.bubble",!0):(b&&a.recordException(b),a.setStatus({code:aJ.ERROR,message:null==b?void 0:b.message})),a.end()},aO=new Map,aP=d.createContextKey("next.rootSpanId"),aQ=0,aR={set(a,b,c){a.push({key:b,value:c})}};class aS{getTracerInstance(){return aI.getTracer("next.js","0.0.1")}getContext(){return aG}getTracePropagationData(){let a=aG.active(),b=[];return aH.inject(a,b,aR),b}getActiveScopeSpan(){return aI.getSpan(null==aG?void 0:aG.active())}withPropagatedContext(a,b,c){let d=aG.active();if(aI.getSpanContext(d))return b();let e=aH.extract(d,a,c);return aG.with(e,b)}trace(...a){var b;let[c,d,e]=a,{fn:f,options:g}="function"==typeof d?{fn:d,options:{}}:{fn:e,options:{...d}},h=g.spanName??c;if(!aD.includes(c)&&"1"!==process.env.NEXT_OTEL_VERBOSE||g.hideSpan)return f();let i=this.getSpanContext((null==g?void 0:g.parentSpan)??this.getActiveScopeSpan()),j=!1;i?(null==(b=aI.getSpanContext(i))?void 0:b.isRemote)&&(j=!0):(i=(null==aG?void 0:aG.active())??aL,j=!0);let k=aQ++;return g.attributes={"next.span_name":h,"next.span_type":c,...g.attributes},aG.with(i.setValue(aP,k),()=>this.getTracerInstance().startActiveSpan(h,g,a=>{let b="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,d=()=>{aO.delete(k),b&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&aE.includes(c||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(c.split(".").pop()||"").replace(/[A-Z]/g,a=>"-"+a.toLowerCase())}`,{start:b,end:performance.now()})};j&&aO.set(k,new Map(Object.entries(g.attributes??{})));try{if(f.length>1)return f(a,b=>aN(a,b));let b=f(a);if(aF(b))return b.then(b=>(a.end(),b)).catch(b=>{throw aN(a,b),b}).finally(d);return a.end(),d(),b}catch(b){throw aN(a,b),d(),b}}))}wrap(...a){let b=this,[c,d,e]=3===a.length?a:[a[0],{},a[1]];return aD.includes(c)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let a=d;"function"==typeof a&&"function"==typeof e&&(a=a.apply(this,arguments));let f=arguments.length-1,g=arguments[f];if("function"!=typeof g)return b.trace(c,a,()=>e.apply(this,arguments));{let d=b.getContext().bind(aG.active(),g);return b.trace(c,a,(a,b)=>(arguments[f]=function(a){return null==b||b(a),d.apply(this,arguments)},e.apply(this,arguments)))}}:e}startSpan(...a){let[b,c]=a,d=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(b,c,d)}getSpanContext(a){return a?aI.setSpan(aG.active(),a):void 0}getRootSpanAttributes(){let a=aG.active().getValue(aP);return aO.get(a)}setRootSpanAttribute(a,b){let c=aG.active().getValue(aP),d=aO.get(c);d&&d.set(a,b)}}let aT=(()=>{let a=new aS;return()=>a})(),aU="__prerender_bypass";Symbol("__next_preview_data"),Symbol(aU);class aV{constructor(a,b,c,d){var e;let f=a&&function(a,b){let c=af.from(a.headers);return{isOnDemandRevalidate:c.get("x-prerender-revalidate")===b.previewModeId,revalidateOnlyGenerated:c.has("x-prerender-revalidate-if-generated")}}(b,a).isOnDemandRevalidate,g=null==(e=c.get(aU))?void 0:e.value;this._isEnabled=!!(!f&&g&&a&&g===a.previewModeId),this._previewModeId=null==a?void 0:a.previewModeId,this._mutableCookies=d}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:aU,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:aU,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function aW(a,b){if("x-middleware-set-cookie"in a.headers&&"string"==typeof a.headers["x-middleware-set-cookie"]){let c=a.headers["x-middleware-set-cookie"],d=new Headers;for(let a of C(c))d.append("set-cookie",a);for(let a of new V.ResponseCookies(d).getAll())b.set(a)}}var aX=c(802),aY=c.n(aX);class aZ extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}class a${constructor(a,b){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=a,this.calculateSize=b||(()=>1)}set(a,b){if(!a||!b)return;let c=this.calculateSize(b);if(c>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0),this.cache.set(a,b),this.sizes.set(a,c),this.totalSize+=c,this.touch(a)}has(a){return!!a&&(this.touch(a),!!this.cache.get(a))}get(a){if(!a)return;let b=this.cache.get(a);if(void 0!==b)return this.touch(a),b}touch(a){let b=this.cache.get(a);void 0!==b&&(this.cache.delete(a),this.cache.set(a,b),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let a=this.cache.keys().next().value;if(void 0!==a){let b=this.sizes.get(a)||0;this.totalSize-=b,this.cache.delete(a),this.sizes.delete(a)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(a){this.cache.has(a)&&(this.totalSize-=this.sizes.get(a)||0,this.cache.delete(a),this.sizes.delete(a))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}c(356).Buffer,new a$(0x3200000,a=>a.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE&&((a,...b)=>{console.log(`use-cache: ${a}`,...b)}),Symbol.for("@next/cache-handlers");let a_=Symbol.for("@next/cache-handlers-map"),a0=Symbol.for("@next/cache-handlers-set"),a1=globalThis;function a2(){if(a1[a_])return a1[a_].entries()}async function a3(a,b){if(!a)return b();let c=a4(a);try{return await b()}finally{let b=function(a,b){let c=new Set(a.pendingRevalidatedTags),d=new Set(a.pendingRevalidateWrites);return{pendingRevalidatedTags:b.pendingRevalidatedTags.filter(a=>!c.has(a)),pendingRevalidates:Object.fromEntries(Object.entries(b.pendingRevalidates).filter(([b])=>!(b in a.pendingRevalidates))),pendingRevalidateWrites:b.pendingRevalidateWrites.filter(a=>!d.has(a))}}(c,a4(a));await a6(a,b)}}function a4(a){return{pendingRevalidatedTags:a.pendingRevalidatedTags?[...a.pendingRevalidatedTags]:[],pendingRevalidates:{...a.pendingRevalidates},pendingRevalidateWrites:a.pendingRevalidateWrites?[...a.pendingRevalidateWrites]:[]}}async function a5(a,b){if(0===a.length)return;let c=[];b&&c.push(b.revalidateTag(a));let d=function(){if(a1[a0])return a1[a0].values()}();if(d)for(let b of d)c.push(b.expireTags(...a));await Promise.all(c)}async function a6(a,b){let c=(null==b?void 0:b.pendingRevalidatedTags)??a.pendingRevalidatedTags??[],d=(null==b?void 0:b.pendingRevalidates)??a.pendingRevalidates??{},e=(null==b?void 0:b.pendingRevalidateWrites)??a.pendingRevalidateWrites??[];return Promise.all([a5(c,a.incrementalCache),...Object.values(d),...e])}let a7=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a8{disable(){throw a7}getStore(){}run(){throw a7}exit(){throw a7}enterWith(){throw a7}static bind(a){return a}}let a9="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,ba=a9?new a9:new a8;class bb{constructor({waitUntil:a,onClose:b,onTaskError:c}){this.workUnitStores=new Set,this.waitUntil=a,this.onClose=b,this.onTaskError=c,this.callbackQueue=new(aY()),this.callbackQueue.pause()}after(a){if(aF(a))this.waitUntil||bc(),this.waitUntil(a.catch(a=>this.reportTaskError("promise",a)));else if("function"==typeof a)this.addCallback(a);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(a){var b;this.waitUntil||bc();let c=al.getStore();c&&this.workUnitStores.add(c);let d=ba.getStore(),e=d?d.rootTaskSpawnPhase:null==c?void 0:c.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let f=(b=async()=>{try{await ba.run({rootTaskSpawnPhase:e},()=>a())}catch(a){this.reportTaskError("function",a)}},a9?a9.bind(b):a8.bind(b));this.callbackQueue.add(f)}async runCallbacksOnClose(){return await new Promise(a=>this.onClose(a)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let a of this.workUnitStores)a.phase="after";let a=ak.getStore();if(!a)throw Object.defineProperty(new aZ("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return a3(a,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(a,b){if(console.error("promise"===a?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",b),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,b)}catch(a){console.error(Object.defineProperty(new aZ("`onTaskError` threw while handling an error thrown from an `after` task",{cause:a}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function bc(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function bd(a){let b,c={then:(d,e)=>(b||(b=a()),b.then(a=>{c.value=a}).catch(()=>{}),b.then(d,e))};return c}class be{onClose(a){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",a),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function bf(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID||"",previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let bg=Symbol.for("@next/request-context");async function bh(a,b,c){let d=[],e=c&&c.size>0;for(let b of(a=>{let b=["/layout"];if(a.startsWith("/")){let c=a.split("/");for(let a=1;a<c.length+1;a++){let d=c.slice(0,a).join("/");d&&(d.endsWith("/page")||d.endsWith("/route")||(d=`${d}${!d.endsWith("/")?"/":""}layout`),b.push(d))}}return b})(a))b=`${A}${b}`,d.push(b);if(b.pathname&&!e){let a=`${A}${b.pathname}`;d.push(a)}return{tags:d,expirationsByCacheKind:function(a){let b=new Map,c=a2();if(c)for(let[d,e]of c)"getExpiration"in e&&b.set(d,bd(async()=>e.getExpiration(...a)));return b}(d)}}class bi extends X{constructor(a){super(a.input,a.init),this.sourcePage=a.page}get request(){throw Object.defineProperty(new x({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new x({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new x({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let bj={keys:a=>Array.from(a.keys()),get:(a,b)=>a.get(b)??void 0},bk=(a,b)=>aT().withPropagatedContext(a.headers,b,bj),bl=!1;async function bm(a){var b;let d,e;if(!bl&&(bl=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:a,wrapRequestHandler:b}=c(905);a(),bk=b(bk)}await v();let f=void 0!==globalThis.__BUILD_MANIFEST;a.request.url=a.request.url.replace(/\.rsc($|\?)/,"$1");let g=a.bypassNextUrl?new URL(a.request.url):new U(a.request.url,{headers:a.request.headers,nextConfig:a.request.nextConfig});for(let a of[...g.searchParams.keys()]){let b=g.searchParams.getAll(a),c=function(a){for(let b of["nxtP","nxtI"])if(a!==b&&a.startsWith(b))return a.substring(b.length);return null}(a);if(c){for(let a of(g.searchParams.delete(c),b))g.searchParams.append(c,a);g.searchParams.delete(a)}}let h=process.env.__NEXT_BUILD_ID||"";"buildId"in g&&(h=g.buildId||"",g.buildId="");let i=function(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}(a.request.headers),j=i.has("x-nextjs-data"),k="1"===i.get("RSC");j&&"/index"===g.pathname&&(g.pathname="/");let l=new Map;if(!f)for(let a of ad){let b=a.toLowerCase(),c=i.get(b);null!==c&&(l.set(b,c),i.delete(b))}let m=new bi({page:a.page,input:(function(a){let b="string"==typeof a,c=b?new URL(a):a;return c.searchParams.delete("_rsc"),b?c.toString():c})(g).toString(),init:{body:a.request.body,headers:i,method:a.request.method,nextConfig:a.request.nextConfig,signal:a.request.signal}});j&&Object.defineProperty(m,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&a.IncrementalCache&&(globalThis.__incrementalCache=new a.IncrementalCache({CurCacheHandler:a.incrementalCacheHandler,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:a.request.headers,getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:bf()})}));let n=a.request.waitUntil??(null==(b=function(){let a=globalThis[bg];return null==a?void 0:a.get()}())?void 0:b.waitUntil),o=new J({request:m,page:a.page,context:n?{waitUntil:n}:void 0});if((d=await bk(m,()=>{if("/middleware"===a.page||"/src/middleware"===a.page){let b=o.waitUntil.bind(o),c=new be;return aT().trace(aC.execute,{spanName:`middleware ${m.method} ${m.nextUrl.pathname}`,attributes:{"http.target":m.nextUrl.pathname,"http.method":m.method}},async()=>{try{var d,f,g,i,j,k;let l=bf(),n=await bh("/",m.nextUrl,null),p=(j=m.nextUrl,k=a=>{e=a},function(a,b,c,d,e,f,g,h,i,j,k){function l(a){c&&c.setHeader("Set-Cookie",a)}let m={};return{type:"request",phase:a,implicitTags:f,url:{pathname:d.pathname,search:d.search??""},rootParams:e,get headers(){return m.headers||(m.headers=function(a){let b=af.from(a);for(let a of ad)b.delete(a.toLowerCase());return af.seal(b)}(b.headers)),m.headers},get cookies(){if(!m.cookies){let a=new V.RequestCookies(af.from(b.headers));aW(b,a),m.cookies=an.seal(a)}return m.cookies},set cookies(value){m.cookies=value},get mutableCookies(){if(!m.mutableCookies){let a=function(a,b){let c=new V.RequestCookies(af.from(a));return ap.wrap(c,b)}(b.headers,g||(c?l:void 0));aW(b,a),m.mutableCookies=a}return m.mutableCookies},get userspaceMutableCookies(){return m.userspaceMutableCookies||(m.userspaceMutableCookies=function(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return aq("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return aq("cookies().set"),a.set(...c),b};default:return Y.get(a,c,d)}}});return b}(this.mutableCookies)),m.userspaceMutableCookies},get draftMode(){return m.draftMode||(m.draftMode=new aV(i,b,this.cookies,this.mutableCookies)),m.draftMode},renderResumeDataCache:h??null,isHmrRefresh:j,serverComponentsHmrCache:k||globalThis.__serverComponentsHmrCache}}("action",m,void 0,j,{},n,k,void 0,l,!1,void 0)),q=function({page:a,fallbackRouteParams:b,renderOpts:c,requestEndedState:d,isPrefetchRequest:e,buildId:f,previouslyRevalidatedTags:g}){var h;let i={isStaticGeneration:!c.shouldWaitOnAllReady&&!c.supportsDynamicResponse&&!c.isDraftMode&&!c.isPossibleServerAction,page:a,fallbackRouteParams:b,route:(h=a.split("/").reduce((a,b,c,d)=>b?"("===b[0]&&b.endsWith(")")||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b:a,"")).startsWith("/")?h:"/"+h,incrementalCache:c.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:c.cacheLifeProfiles,isRevalidate:c.isRevalidate,isBuildTimePrerendering:c.nextExport,hasReadableErrorStacks:c.hasReadableErrorStacks,fetchCache:c.fetchCache,isOnDemandRevalidate:c.isOnDemandRevalidate,isDraftMode:c.isDraftMode,requestEndedState:d,isPrefetchRequest:e,buildId:f,reactLoadableManifest:(null==c?void 0:c.reactLoadableManifest)||{},assetPrefix:(null==c?void 0:c.assetPrefix)||"",afterContext:function(a){let{waitUntil:b,onClose:c,onAfterTaskError:d}=a;return new bb({waitUntil:b,onClose:c,onTaskError:d})}(c),dynamicIOEnabled:c.experimental.dynamicIO,dev:c.dev??!1,previouslyRevalidatedTags:g,refreshTagsByCacheKind:function(){let a=new Map,b=a2();if(b)for(let[c,d]of b)"refreshTags"in d&&a.set(c,bd(async()=>d.refreshTags()));return a}(),runInCleanSnapshot:a9?a9.snapshot():function(a,...b){return a(...b)}};return c.store=i,i}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(f=a.request.nextConfig)||null==(d=f.experimental)?void 0:d.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(i=a.request.nextConfig)||null==(g=i.experimental)?void 0:g.authInterrupts)},supportsDynamicResponse:!0,waitUntil:b,onClose:c.onClose.bind(c),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:m.headers.has(ac),buildId:h??"",previouslyRevalidatedTags:[]});return await ak.run(q,()=>al.run(p,a.handler,m,o))}finally{setTimeout(()=>{c.dispatchClose()},0)}})}return a.handler(m,o)}))&&!(d instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});d&&e&&d.headers.set("set-cookie",e);let p=null==d?void 0:d.headers.get("x-middleware-rewrite");if(d&&p&&(k||!f)){let b=new U(p,{forceLocale:!0,headers:a.request.headers,nextConfig:a.request.nextConfig});f||b.host!==m.nextUrl.host||(b.buildId=h||b.buildId,d.headers.set("x-middleware-rewrite",String(b)));let{url:c,isRelative:e}=ab(b.toString(),g.toString());!f&&j&&d.headers.set("x-nextjs-rewrite",c),k&&e&&(g.pathname!==b.pathname&&d.headers.set("x-nextjs-rewritten-path",b.pathname),g.search!==b.search&&d.headers.set("x-nextjs-rewritten-query",b.search.slice(1)))}let q=null==d?void 0:d.headers.get("Location");if(d&&q&&!f){let b=new U(q,{forceLocale:!1,headers:a.request.headers,nextConfig:a.request.nextConfig});d=new Response(d.body,d),b.host===g.host&&(b.buildId=h||b.buildId,d.headers.set("Location",b.toString())),j&&(d.headers.delete("Location"),d.headers.set("x-nextjs-redirect",ab(b.toString(),g.toString()).url))}let r=d||aa.next(),s=r.headers.get("x-middleware-override-headers"),t=[];if(s){for(let[a,b]of l)r.headers.set(`x-middleware-request-${a}`,b),t.push(a);t.length>0&&r.headers.set("x-middleware-override-headers",s+","+t.join(","))}return{response:r,waitUntil:("internal"===o[H].kind?Promise.all(o[H].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:m.fetchMetrics}}var bn=c(554);function bo(){return"undefined"!=typeof window&&void 0!==window.document}let bp={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},bq=/^(.*)[.](0|[1-9][0-9]*)$/;function br(a,b){if(a===b)return!0;let c=a.match(bq);return!!c&&c[1]===b}function bs(a,b,c){let d=c??3180,e=encodeURIComponent(b);if(e.length<=d)return[{name:a,value:b}];let f=[];for(;e.length>0;){let a=e.slice(0,d),b=a.lastIndexOf("%");b>d-3&&(a=a.slice(0,b));let c="";for(;a.length>0;)try{c=decodeURIComponent(a);break}catch(b){if(b instanceof URIError&&"%"===a.at(-3)&&a.length>3)a=a.slice(0,a.length-3);else throw b}f.push(c),e=e.slice(a.length)}return f.map((b,c)=>({name:`${a}.${c}`,value:b}))}async function bt(a,b){let c=await b(a);if(c)return c;let d=[];for(let c=0;;c++){let e=`${a}.${c}`,f=await b(e);if(!f)break;d.push(f)}return d.length>0?d.join(""):null}let bu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),bv=" 	\n\r=".split(""),bw=(()=>{let a=Array(128);for(let b=0;b<a.length;b+=1)a[b]=-1;for(let b=0;b<bv.length;b+=1)a[bv[b].charCodeAt(0)]=-2;for(let b=0;b<bu.length;b+=1)a[bu[b].charCodeAt(0)]=b;return a})();function bx(a){let b=[],c=0,d=0;if(function(a,b){for(let c=0;c<a.length;c+=1){let d=a.charCodeAt(c);if(d>55295&&d<=56319){let b=(d-55296)*1024&65535;d=(a.charCodeAt(c+1)-56320&65535|b)+65536,c+=1}!function(a,b){if(a<=127)return b(a);if(a<=2047){b(192|a>>6),b(128|63&a);return}if(a<=65535){b(224|a>>12),b(128|a>>6&63),b(128|63&a);return}if(a<=1114111){b(240|a>>18),b(128|a>>12&63),b(128|a>>6&63),b(128|63&a);return}throw Error(`Unrecognized Unicode codepoint: ${a.toString(16)}`)}(d,b)}}(a,a=>{for(c=c<<8|a,d+=8;d>=6;){let a=c>>d-6&63;b.push(bu[a]),d-=6}}),d>0)for(c<<=6-d,d=6;d>=6;){let a=c>>d-6&63;b.push(bu[a]),d-=6}return b.join("")}function by(a){let b=[],c=a=>{b.push(String.fromCodePoint(a))},d={utf8seq:0,codepoint:0},e=0,f=0;for(let b=0;b<a.length;b+=1){let g=bw[a.charCodeAt(b)];if(g>-1)for(e=e<<6|g,f+=6;f>=8;)(function(a,b,c){if(0===b.utf8seq){if(a<=127)return c(a);for(let c=1;c<6;c+=1)if((a>>7-c&1)==0){b.utf8seq=c;break}if(2===b.utf8seq)b.codepoint=31&a;else if(3===b.utf8seq)b.codepoint=15&a;else if(4===b.utf8seq)b.codepoint=7&a;else throw Error("Invalid UTF-8 sequence");b.utf8seq-=1}else if(b.utf8seq>0){if(a<=127)throw Error("Invalid UTF-8 sequence");b.codepoint=b.codepoint<<6|63&a,b.utf8seq-=1,0===b.utf8seq&&c(b.codepoint)}})(e>>f-8&255,d,c),f-=8;else if(-2===g)continue;else throw Error(`Invalid Base64-URL character "${a.at(b)}" at position ${b}`)}return b.join("")}let bz="base64-";async function bA({getAll:a,setAll:b,setItems:c,removedItems:d},e){let f=e.cookieEncoding,g=e.cookieOptions??null,h=await a([...c?Object.keys(c):[],...d?Object.keys(d):[]]),i=h?.map(({name:a})=>a)||[],j=Object.keys(d).flatMap(a=>i.filter(b=>br(b,a))),k=Object.keys(c).flatMap(a=>{let b=new Set(i.filter(b=>br(b,a))),d=c[a];"base64url"===f&&(d=bz+bx(d));let e=bs(a,d);return e.forEach(a=>{b.delete(a.name)}),j.push(...b),e}),l={...bp,...g,maxAge:0},m={...bp,...g,maxAge:bp.maxAge};delete l.name,delete m.name,await b([...j.map(a=>({name:a,value:"",options:l})),...k.map(({name:a,value:b})=>({name:a,value:b,options:m}))])}class bB extends Error{constructor(a,b="FunctionsError",c){super(a),this.name=b,this.context=c}}class bC extends bB{constructor(a){super("Failed to send a request to the Edge Function","FunctionsFetchError",a)}}class bD extends bB{constructor(a){super("Relay Error invoking the Edge Function","FunctionsRelayError",a)}}class bE extends bB{constructor(a){super("Edge Function returned a non-2xx status code","FunctionsHttpError",a)}}!function(a){a.Any="any",a.ApNortheast1="ap-northeast-1",a.ApNortheast2="ap-northeast-2",a.ApSouth1="ap-south-1",a.ApSoutheast1="ap-southeast-1",a.ApSoutheast2="ap-southeast-2",a.CaCentral1="ca-central-1",a.EuCentral1="eu-central-1",a.EuWest1="eu-west-1",a.EuWest2="eu-west-2",a.EuWest3="eu-west-3",a.SaEast1="sa-east-1",a.UsEast1="us-east-1",a.UsWest1="us-west-1",a.UsWest2="us-west-2"}(e||(e={}));class bF{constructor(a,{headers:b={},customFetch:d,region:f=e.Any}={}){this.url=a,this.headers=b,this.region=f,this.fetch=(a=>{let b;return b=a||("undefined"==typeof fetch?(...a)=>Promise.resolve().then(c.bind(c,3)).then(({default:b})=>b(...a)):fetch),(...a)=>b(...a)})(d)}setAuth(a){this.headers.Authorization=`Bearer ${a}`}invoke(a,b={}){var c,d,e,f,g;return d=this,e=void 0,f=void 0,g=function*(){try{let d,{headers:e,method:f,body:g}=b,h={},{region:i}=b;i||(i=this.region);let j=new URL(`${this.url}/${a}`);i&&"any"!==i&&(h["x-region"]=i,j.searchParams.set("forceFunctionRegion",i)),g&&(e&&!Object.prototype.hasOwnProperty.call(e,"Content-Type")||!e)&&("undefined"!=typeof Blob&&g instanceof Blob||g instanceof ArrayBuffer?(h["Content-Type"]="application/octet-stream",d=g):"string"==typeof g?(h["Content-Type"]="text/plain",d=g):"undefined"!=typeof FormData&&g instanceof FormData?d=g:(h["Content-Type"]="application/json",d=JSON.stringify(g)));let k=yield this.fetch(j.toString(),{method:f||"POST",headers:Object.assign(Object.assign(Object.assign({},h),this.headers),e),body:d}).catch(a=>{throw new bC(a)}),l=k.headers.get("x-relay-error");if(l&&"true"===l)throw new bD(k);if(!k.ok)throw new bE(k);let m=(null!=(c=k.headers.get("Content-Type"))?c:"text/plain").split(";")[0].trim();return{data:"application/json"===m?yield k.json():"application/octet-stream"===m?yield k.blob():"text/event-stream"===m?k:"multipart/form-data"===m?yield k.formData():yield k.text(),error:null,response:k}}catch(a){return{data:null,error:a,response:a instanceof bE||a instanceof bD?a.context:void 0}}},new(f||(f=Promise))(function(a,b){function c(a){try{i(g.next(a))}catch(a){b(a)}}function h(a){try{i(g.throw(a))}catch(a){b(a)}}function i(b){var d;b.done?a(b.value):((d=b.value)instanceof f?d:new f(function(a){a(d)})).then(c,h)}i((g=g.apply(d,e||[])).next())})}}let{PostgrestClient:bG,PostgrestQueryBuilder:bH,PostgrestFilterBuilder:bI,PostgrestTransformBuilder:bJ,PostgrestBuilder:bK,PostgrestError:bL}=c(355),bM=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw Error("`WebSocket` is not supported in this environment")}();!function(a){a[a.connecting=0]="connecting",a[a.open=1]="open",a[a.closing=2]="closing",a[a.closed=3]="closed"}(f||(f={})),function(a){a.closed="closed",a.errored="errored",a.joined="joined",a.joining="joining",a.leaving="leaving"}(g||(g={})),function(a){a.close="phx_close",a.error="phx_error",a.join="phx_join",a.reply="phx_reply",a.leave="phx_leave",a.access_token="access_token"}(h||(h={})),(i||(i={})).websocket="websocket",function(a){a.Connecting="connecting",a.Open="open",a.Closing="closing",a.Closed="closed"}(j||(j={}));class bN{constructor(){this.HEADER_LENGTH=1}decode(a,b){return a.constructor===ArrayBuffer?b(this._binaryDecode(a)):"string"==typeof a?b(JSON.parse(a)):b({})}_binaryDecode(a){let b=new DataView(a),c=new TextDecoder;return this._decodeBroadcast(a,b,c)}_decodeBroadcast(a,b,c){let d=b.getUint8(1),e=b.getUint8(2),f=this.HEADER_LENGTH+2,g=c.decode(a.slice(f,f+d));f+=d;let h=c.decode(a.slice(f,f+e));return f+=e,{ref:null,topic:g,event:h,payload:JSON.parse(c.decode(a.slice(f,a.byteLength)))}}}class bO{constructor(a,b){this.callback=a,this.timerCalc=b,this.timer=void 0,this.tries=0,this.callback=a,this.timerCalc=b}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(a){a.abstime="abstime",a.bool="bool",a.date="date",a.daterange="daterange",a.float4="float4",a.float8="float8",a.int2="int2",a.int4="int4",a.int4range="int4range",a.int8="int8",a.int8range="int8range",a.json="json",a.jsonb="jsonb",a.money="money",a.numeric="numeric",a.oid="oid",a.reltime="reltime",a.text="text",a.time="time",a.timestamp="timestamp",a.timestamptz="timestamptz",a.timetz="timetz",a.tsrange="tsrange",a.tstzrange="tstzrange"}(k||(k={}));let bP=(a,b,c={})=>{var d;let e=null!=(d=c.skipTypes)?d:[];return Object.keys(b).reduce((c,d)=>(c[d]=bQ(d,a,b,e),c),{})},bQ=(a,b,c,d)=>{let e=b.find(b=>b.name===a),f=null==e?void 0:e.type,g=c[a];return f&&!d.includes(f)?bR(f,g):bS(g)},bR=(a,b)=>{if("_"===a.charAt(0))return bW(b,a.slice(1,a.length));switch(a){case k.bool:return bT(b);case k.float4:case k.float8:case k.int2:case k.int4:case k.int8:case k.numeric:case k.oid:return bU(b);case k.json:case k.jsonb:return bV(b);case k.timestamp:return bX(b);case k.abstime:case k.date:case k.daterange:case k.int4range:case k.int8range:case k.money:case k.reltime:case k.text:case k.time:case k.timestamptz:case k.timetz:case k.tsrange:case k.tstzrange:default:return bS(b)}},bS=a=>a,bT=a=>{switch(a){case"t":return!0;case"f":return!1;default:return a}},bU=a=>{if("string"==typeof a){let b=parseFloat(a);if(!Number.isNaN(b))return b}return a},bV=a=>{if("string"==typeof a)try{return JSON.parse(a)}catch(a){console.log(`JSON parse error: ${a}`)}return a},bW=(a,b)=>{if("string"!=typeof a)return a;let c=a.length-1,d=a[c];if("{"===a[0]&&"}"===d){let d,e=a.slice(1,c);try{d=JSON.parse("["+e+"]")}catch(a){d=e?e.split(","):[]}return d.map(a=>bR(b,a))}return a},bX=a=>"string"==typeof a?a.replace(" ","T"):a,bY=a=>{let b=a;return(b=(b=b.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class bZ{constructor(a,b,c={},d=1e4){this.channel=a,this.event=b,this.payload=c,this.timeout=d,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(a){this.timeout=a,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(a){this.payload=Object.assign(Object.assign({},this.payload),a)}receive(a,b){var c;return this._hasReceived(a)&&b(null==(c=this.receivedResp)?void 0:c.response),this.recHooks.push({status:a,callback:b}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},a=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=a,this._matchReceive(a)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(a,b){this.refEvent&&this.channel._trigger(this.refEvent,{status:a,response:b})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:a,response:b}){this.recHooks.filter(b=>b.status===a).forEach(a=>a.callback(b))}_hasReceived(a){return this.receivedResp&&this.receivedResp.status===a}}!function(a){a.SYNC="sync",a.JOIN="join",a.LEAVE="leave"}(l||(l={}));class b${constructor(a,b){this.channel=a,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let c=(null==b?void 0:b.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(c.state,{},a=>{let{onJoin:b,onLeave:c,onSync:d}=this.caller;this.joinRef=this.channel._joinRef(),this.state=b$.syncState(this.state,a,b,c),this.pendingDiffs.forEach(a=>{this.state=b$.syncDiff(this.state,a,b,c)}),this.pendingDiffs=[],d()}),this.channel._on(c.diff,{},a=>{let{onJoin:b,onLeave:c,onSync:d}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(a):(this.state=b$.syncDiff(this.state,a,b,c),d())}),this.onJoin((a,b,c)=>{this.channel._trigger("presence",{event:"join",key:a,currentPresences:b,newPresences:c})}),this.onLeave((a,b,c)=>{this.channel._trigger("presence",{event:"leave",key:a,currentPresences:b,leftPresences:c})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(a,b,c,d){let e=this.cloneDeep(a),f=this.transformState(b),g={},h={};return this.map(e,(a,b)=>{f[a]||(h[a]=b)}),this.map(f,(a,b)=>{let c=e[a];if(c){let d=b.map(a=>a.presence_ref),e=c.map(a=>a.presence_ref),f=b.filter(a=>0>e.indexOf(a.presence_ref)),i=c.filter(a=>0>d.indexOf(a.presence_ref));f.length>0&&(g[a]=f),i.length>0&&(h[a]=i)}else g[a]=b}),this.syncDiff(e,{joins:g,leaves:h},c,d)}static syncDiff(a,b,c,d){let{joins:e,leaves:f}={joins:this.transformState(b.joins),leaves:this.transformState(b.leaves)};return c||(c=()=>{}),d||(d=()=>{}),this.map(e,(b,d)=>{var e;let f=null!=(e=a[b])?e:[];if(a[b]=this.cloneDeep(d),f.length>0){let c=a[b].map(a=>a.presence_ref),d=f.filter(a=>0>c.indexOf(a.presence_ref));a[b].unshift(...d)}c(b,f,d)}),this.map(f,(b,c)=>{let e=a[b];if(!e)return;let f=c.map(a=>a.presence_ref);e=e.filter(a=>0>f.indexOf(a.presence_ref)),a[b]=e,d(b,e,c),0===e.length&&delete a[b]}),a}static map(a,b){return Object.getOwnPropertyNames(a).map(c=>b(c,a[c]))}static transformState(a){return Object.getOwnPropertyNames(a=this.cloneDeep(a)).reduce((b,c)=>{let d=a[c];return"metas"in d?b[c]=d.metas.map(a=>(a.presence_ref=a.phx_ref,delete a.phx_ref,delete a.phx_ref_prev,a)):b[c]=d,b},{})}static cloneDeep(a){return JSON.parse(JSON.stringify(a))}onJoin(a){this.caller.onJoin=a}onLeave(a){this.caller.onLeave=a}onSync(a){this.caller.onSync=a}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(a){a.ALL="*",a.INSERT="INSERT",a.UPDATE="UPDATE",a.DELETE="DELETE"}(m||(m={})),function(a){a.BROADCAST="broadcast",a.PRESENCE="presence",a.POSTGRES_CHANGES="postgres_changes",a.SYSTEM="system"}(n||(n={})),function(a){a.SUBSCRIBED="SUBSCRIBED",a.TIMED_OUT="TIMED_OUT",a.CLOSED="CLOSED",a.CHANNEL_ERROR="CHANNEL_ERROR"}(o||(o={}));class b_{constructor(a,b={config:{}},c){this.topic=a,this.params=b,this.socket=c,this.bindings={},this.state=g.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=a.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},b.config),this.timeout=this.socket.timeout,this.joinPush=new bZ(this,h.join,this.params,this.timeout),this.rejoinTimer=new bO(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=g.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(a=>a.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=g.closed,this.socket._remove(this)}),this._onError(a=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,a),this.state=g.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=g.errored,this.rejoinTimer.scheduleTimeout())}),this._on(h.reply,{},(a,b)=>{this._trigger(this._replyEventName(b),a)}),this.presence=new b$(this),this.broadcastEndpointURL=bY(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(a,b=this.timeout){var c,d;if(this.socket.isConnected()||this.socket.connect(),this.state==g.closed){let{config:{broadcast:e,presence:f,private:h}}=this.params;this._onError(b=>null==a?void 0:a(o.CHANNEL_ERROR,b)),this._onClose(()=>null==a?void 0:a(o.CLOSED));let i={},j={broadcast:e,presence:f,postgres_changes:null!=(d=null==(c=this.bindings.postgres_changes)?void 0:c.map(a=>a.filter))?d:[],private:h};this.socket.accessTokenValue&&(i.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:j},i)),this.joinedOnce=!0,this._rejoin(b),this.joinPush.receive("ok",async({postgres_changes:b})=>{var c;if(this.socket.setAuth(),void 0===b){null==a||a(o.SUBSCRIBED);return}{let d=this.bindings.postgres_changes,e=null!=(c=null==d?void 0:d.length)?c:0,f=[];for(let c=0;c<e;c++){let e=d[c],{filter:{event:h,schema:i,table:j,filter:k}}=e,l=b&&b[c];if(l&&l.event===h&&l.schema===i&&l.table===j&&l.filter===k)f.push(Object.assign(Object.assign({},e),{id:l.id}));else{this.unsubscribe(),this.state=g.errored,null==a||a(o.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=f,a&&a(o.SUBSCRIBED);return}}).receive("error",b=>{this.state=g.errored,null==a||a(o.CHANNEL_ERROR,Error(JSON.stringify(Object.values(b).join(", ")||"error")))}).receive("timeout",()=>{null==a||a(o.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(a,b={}){return await this.send({type:"presence",event:"track",payload:a},b.timeout||this.timeout)}async untrack(a={}){return await this.send({type:"presence",event:"untrack"},a)}on(a,b,c){return this._on(a,b,c)}async send(a,b={}){var c,d;if(this._canPush()||"broadcast"!==a.type)return new Promise(c=>{var d,e,f;let g=this._push(a.type,a,b.timeout||this.timeout);"broadcast"!==a.type||(null==(f=null==(e=null==(d=this.params)?void 0:d.config)?void 0:e.broadcast)?void 0:f.ack)||c("ok"),g.receive("ok",()=>c("ok")),g.receive("error",()=>c("error")),g.receive("timeout",()=>c("timed out"))});{let{event:e,payload:f}=a,g={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:e,payload:f,private:this.private}]})};try{let a=await this._fetchWithTimeout(this.broadcastEndpointURL,g,null!=(c=b.timeout)?c:this.timeout);return await (null==(d=a.body)?void 0:d.cancel()),a.ok?"ok":"error"}catch(a){if("AbortError"===a.name)return"timed out";return"error"}}}updateJoinPayload(a){this.joinPush.updatePayload(a)}unsubscribe(a=this.timeout){this.state=g.leaving;let b=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(h.close,"leave",this._joinRef())};this.joinPush.destroy();let c=null;return new Promise(d=>{(c=new bZ(this,h.leave,{},a)).receive("ok",()=>{b(),d("ok")}).receive("timeout",()=>{b(),d("timed out")}).receive("error",()=>{d("error")}),c.send(),this._canPush()||c.trigger("ok",{})}).finally(()=>{null==c||c.destroy()})}teardown(){this.pushBuffer.forEach(a=>a.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(a,b,c){let d=new AbortController,e=setTimeout(()=>d.abort(),c),f=await this.socket.fetch(a,Object.assign(Object.assign({},b),{signal:d.signal}));return clearTimeout(e),f}_push(a,b,c=this.timeout){if(!this.joinedOnce)throw`tried to push '${a}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let d=new bZ(this,a,b,c);return this._canPush()?d.send():(d.startTimeout(),this.pushBuffer.push(d)),d}_onMessage(a,b,c){return b}_isMember(a){return this.topic===a}_joinRef(){return this.joinPush.ref}_trigger(a,b,c){var d,e;let f=a.toLocaleLowerCase(),{close:g,error:i,leave:j,join:k}=h;if(c&&[g,i,j,k].indexOf(f)>=0&&c!==this._joinRef())return;let l=this._onMessage(f,b,c);if(b&&!l)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?null==(d=this.bindings.postgres_changes)||d.filter(a=>{var b,c,d;return(null==(b=a.filter)?void 0:b.event)==="*"||(null==(d=null==(c=a.filter)?void 0:c.event)?void 0:d.toLocaleLowerCase())===f}).map(a=>a.callback(l,c)):null==(e=this.bindings[f])||e.filter(a=>{var c,d,e,g,h,i;if(!["broadcast","presence","postgres_changes"].includes(f))return a.type.toLocaleLowerCase()===f;if("id"in a){let f=a.id,g=null==(c=a.filter)?void 0:c.event;return f&&(null==(d=b.ids)?void 0:d.includes(f))&&("*"===g||(null==g?void 0:g.toLocaleLowerCase())===(null==(e=b.data)?void 0:e.type.toLocaleLowerCase()))}{let c=null==(h=null==(g=null==a?void 0:a.filter)?void 0:g.event)?void 0:h.toLocaleLowerCase();return"*"===c||c===(null==(i=null==b?void 0:b.event)?void 0:i.toLocaleLowerCase())}}).map(a=>{if("object"==typeof l&&"ids"in l){let a=l.data,{schema:b,table:c,commit_timestamp:d,type:e,errors:f}=a;l=Object.assign(Object.assign({},{schema:b,table:c,commit_timestamp:d,eventType:e,new:{},old:{},errors:f}),this._getPayloadRecords(a))}a.callback(l,c)})}_isClosed(){return this.state===g.closed}_isJoined(){return this.state===g.joined}_isJoining(){return this.state===g.joining}_isLeaving(){return this.state===g.leaving}_replyEventName(a){return`chan_reply_${a}`}_on(a,b,c){let d=a.toLocaleLowerCase(),e={type:d,filter:b,callback:c};return this.bindings[d]?this.bindings[d].push(e):this.bindings[d]=[e],this}_off(a,b){let c=a.toLocaleLowerCase();return this.bindings[c]=this.bindings[c].filter(a=>{var d;return!((null==(d=a.type)?void 0:d.toLocaleLowerCase())===c&&b_.isEqual(a.filter,b))}),this}static isEqual(a,b){if(Object.keys(a).length!==Object.keys(b).length)return!1;for(let c in a)if(a[c]!==b[c])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(a){this._on(h.close,{},a)}_onError(a){this._on(h.error,{},b=>a(b))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(a=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=g.joining,this.joinPush.resend(a))}_getPayloadRecords(a){let b={new:{},old:{}};return("INSERT"===a.type||"UPDATE"===a.type)&&(b.new=bP(a.columns,a.record)),("UPDATE"===a.type||"DELETE"===a.type)&&(b.old=bP(a.columns,a.old_record)),b}}let b0=()=>{},b1=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class b2{constructor(a,b){var d;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=b0,this.ref=0,this.logger=b0,this.conn=null,this.sendBuffer=[],this.serializer=new bN,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=a=>{let b;return b=a||("undefined"==typeof fetch?(...a)=>Promise.resolve().then(c.bind(c,3)).then(({default:b})=>b(...a)):fetch),(...a)=>b(...a)},this.endPoint=`${a}/${i.websocket}`,this.httpEndpoint=bY(a),(null==b?void 0:b.transport)?this.transport=b.transport:this.transport=null,(null==b?void 0:b.params)&&(this.params=b.params),(null==b?void 0:b.timeout)&&(this.timeout=b.timeout),(null==b?void 0:b.logger)&&(this.logger=b.logger),((null==b?void 0:b.logLevel)||(null==b?void 0:b.log_level))&&(this.logLevel=b.logLevel||b.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==b?void 0:b.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=b.heartbeatIntervalMs);let e=null==(d=null==b?void 0:b.params)?void 0:d.apikey;if(e&&(this.accessTokenValue=e,this.apiKey=e),this.reconnectAfterMs=(null==b?void 0:b.reconnectAfterMs)?b.reconnectAfterMs:a=>[1e3,2e3,5e3,1e4][a-1]||1e4,this.encode=(null==b?void 0:b.encode)?b.encode:(a,b)=>b(JSON.stringify(a)),this.decode=(null==b?void 0:b.decode)?b.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new bO(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==b?void 0:b.fetch),null==b?void 0:b.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==b?void 0:b.worker)||!1,this.workerUrl=null==b?void 0:b.workerUrl}this.accessToken=(null==b?void 0:b.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=bM),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(a,b){this.conn&&(this.conn.onclose=function(){},a?this.conn.close(a,null!=b?b:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(a=>a.teardown()))}getChannels(){return this.channels}async removeChannel(a){let b=await a.unsubscribe();return 0===this.channels.length&&this.disconnect(),b}async removeAllChannels(){let a=await Promise.all(this.channels.map(a=>a.unsubscribe()));return this.channels=[],this.disconnect(),a}log(a,b,c){this.logger(a,b,c)}connectionState(){switch(this.conn&&this.conn.readyState){case f.connecting:return j.Connecting;case f.open:return j.Open;case f.closing:return j.Closing;default:return j.Closed}}isConnected(){return this.connectionState()===j.Open}channel(a,b={config:{}}){let c=`realtime:${a}`,d=this.getChannels().find(a=>a.topic===c);if(d)return d;{let c=new b_(`realtime:${a}`,b,this);return this.channels.push(c),c}}push(a){let{topic:b,event:c,payload:d,ref:e}=a,f=()=>{this.encode(a,a=>{var b;null==(b=this.conn)||b.send(a)})};this.log("push",`${b} ${c} (${e})`,d),this.isConnected()?f():this.sendBuffer.push(f)}async setAuth(a=null){let b=a||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=b&&(this.accessTokenValue=b,this.channels.forEach(a=>{b&&a.updateJoinPayload({access_token:b,version:"realtime-js/2.11.15"}),a.joinedOnce&&a._isJoined()&&a._push(h.access_token,{access_token:b})}))}async sendHeartbeat(){var a;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(a=this.conn)||a.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(a){this.heartbeatCallback=a}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(a=>a()),this.sendBuffer=[])}_makeRef(){let a=this.ref+1;return a===this.ref?this.ref=0:this.ref=a,this.ref.toString()}_leaveOpenTopic(a){let b=this.channels.find(b=>b.topic===a&&(b._isJoined()||b._isJoining()));b&&(this.log("transport",`leaving duplicate topic "${a}"`),b.unsubscribe())}_remove(a){this.channels=this.channels.filter(b=>b.topic!==a.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=a=>this._onConnError(a),this.conn.onmessage=a=>this._onConnMessage(a),this.conn.onclose=a=>this._onConnClose(a))}_onConnMessage(a){this.decode(a.data,a=>{let{topic:b,event:c,payload:d,ref:e}=a;"phoenix"===b&&"phx_reply"===c&&this.heartbeatCallback("ok"==a.payload.status?"ok":"error"),e&&e===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${d.status||""} ${b} ${c} ${e&&"("+e+")"||""}`,d),Array.from(this.channels).filter(a=>a._isMember(b)).forEach(a=>a._trigger(c,d,e)),this.stateChangeCallbacks.message.forEach(b=>b(a))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(a=>a())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let a=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(a),this.workerRef.onerror=a=>{this.log("worker","worker error",a.message),this.workerRef.terminate()},this.workerRef.onmessage=a=>{"keepAlive"===a.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(a){this.log("transport","close",a),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(b=>b(a))}_onConnError(a){this.log("transport",`${a}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(b=>b(a))}_triggerChanError(){this.channels.forEach(a=>a._trigger(h.error))}_appendParams(a,b){if(0===Object.keys(b).length)return a;let c=a.match(/\?/)?"&":"?",d=new URLSearchParams(b);return`${a}${c}${d}`}_workerObjectUrl(a){let b;if(a)b=a;else{let a=new Blob([b1],{type:"application/javascript"});b=URL.createObjectURL(a)}return b}}class b3 extends Error{constructor(a){super(a),this.__isStorageError=!0,this.name="StorageError"}}function b4(a){return"object"==typeof a&&null!==a&&"__isStorageError"in a}class b5 extends b3{constructor(a,b){super(a),this.name="StorageApiError",this.status=b}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class b6 extends b3{constructor(a,b){super(a),this.name="StorageUnknownError",this.originalError=b}}let b7=a=>{let b;return b=a||("undefined"==typeof fetch?(...a)=>Promise.resolve().then(c.bind(c,3)).then(({default:b})=>b(...a)):fetch),(...a)=>b(...a)},b8=a=>{if(Array.isArray(a))return a.map(a=>b8(a));if("function"==typeof a||a!==Object(a))return a;let b={};return Object.entries(a).forEach(([a,c])=>{b[a.replace(/([-_][a-z])/gi,a=>a.toUpperCase().replace(/[-_]/g,""))]=b8(c)}),b};var b9=function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let ca=a=>a.msg||a.message||a.error_description||a.error||JSON.stringify(a);function cb(a,b,d,e,f,g){return b9(this,void 0,void 0,function*(){return new Promise((h,i)=>{a(d,((a,b,c,d)=>{let e={method:a,headers:(null==b?void 0:b.headers)||{}};return"GET"===a?e:(e.headers=Object.assign({"Content-Type":"application/json"},null==b?void 0:b.headers),d&&(e.body=JSON.stringify(d)),Object.assign(Object.assign({},e),c))})(b,e,f,g)).then(a=>{if(!a.ok)throw a;return(null==e?void 0:e.noResolveJson)?a:a.json()}).then(a=>h(a)).catch(a=>b9(void 0,void 0,void 0,function*(){var b,d,f,g;let h=yield(b=void 0,d=void 0,f=void 0,g=function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(c.bind(c,3))).Response:Response},new(f||(f=Promise))(function(a,c){function e(a){try{i(g.next(a))}catch(a){c(a)}}function h(a){try{i(g.throw(a))}catch(a){c(a)}}function i(b){var c;b.done?a(b.value):((c=b.value)instanceof f?c:new f(function(a){a(c)})).then(e,h)}i((g=g.apply(b,d||[])).next())}));a instanceof h&&!(null==e?void 0:e.noResolveJson)?a.json().then(b=>{i(new b5(ca(b),a.status||500))}).catch(a=>{i(new b6(ca(a),a))}):i(new b6(ca(a),a))}))})})}function cc(a,b,c,d){return b9(this,void 0,void 0,function*(){return cb(a,"GET",b,c,d)})}function cd(a,b,c,d,e){return b9(this,void 0,void 0,function*(){return cb(a,"POST",b,d,e,c)})}function ce(a,b,c,d,e){return b9(this,void 0,void 0,function*(){return cb(a,"DELETE",b,d,e,c)})}var cf=c(356).Buffer,cg=function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};let ch={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ci={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class cj{constructor(a,b={},c,d){this.url=a,this.headers=b,this.bucketId=c,this.fetch=b7(d)}uploadOrUpdate(a,b,c,d){return cg(this,void 0,void 0,function*(){try{let e,f=Object.assign(Object.assign({},ci),d),g=Object.assign(Object.assign({},this.headers),"POST"===a&&{"x-upsert":String(f.upsert)}),h=f.metadata;"undefined"!=typeof Blob&&c instanceof Blob?((e=new FormData).append("cacheControl",f.cacheControl),h&&e.append("metadata",this.encodeMetadata(h)),e.append("",c)):"undefined"!=typeof FormData&&c instanceof FormData?((e=c).append("cacheControl",f.cacheControl),h&&e.append("metadata",this.encodeMetadata(h))):(e=c,g["cache-control"]=`max-age=${f.cacheControl}`,g["content-type"]=f.contentType,h&&(g["x-metadata"]=this.toBase64(this.encodeMetadata(h)))),(null==d?void 0:d.headers)&&(g=Object.assign(Object.assign({},g),d.headers));let i=this._removeEmptyFolders(b),j=this._getFinalPath(i),k=yield this.fetch(`${this.url}/object/${j}`,Object.assign({method:a,body:e,headers:g},(null==f?void 0:f.duplex)?{duplex:f.duplex}:{})),l=yield k.json();if(k.ok)return{data:{path:i,id:l.Id,fullPath:l.Key},error:null};return{data:null,error:l}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}upload(a,b,c){return cg(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",a,b,c)})}uploadToSignedUrl(a,b,c,d){return cg(this,void 0,void 0,function*(){let e=this._removeEmptyFolders(a),f=this._getFinalPath(e),g=new URL(this.url+`/object/upload/sign/${f}`);g.searchParams.set("token",b);try{let a,b=Object.assign({upsert:ci.upsert},d),f=Object.assign(Object.assign({},this.headers),{"x-upsert":String(b.upsert)});"undefined"!=typeof Blob&&c instanceof Blob?((a=new FormData).append("cacheControl",b.cacheControl),a.append("",c)):"undefined"!=typeof FormData&&c instanceof FormData?(a=c).append("cacheControl",b.cacheControl):(a=c,f["cache-control"]=`max-age=${b.cacheControl}`,f["content-type"]=b.contentType);let h=yield this.fetch(g.toString(),{method:"PUT",body:a,headers:f}),i=yield h.json();if(h.ok)return{data:{path:e,fullPath:i.Key},error:null};return{data:null,error:i}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(a,b){return cg(this,void 0,void 0,function*(){try{let c=this._getFinalPath(a),d=Object.assign({},this.headers);(null==b?void 0:b.upsert)&&(d["x-upsert"]="true");let e=yield cd(this.fetch,`${this.url}/object/upload/sign/${c}`,{},{headers:d}),f=new URL(this.url+e.url),g=f.searchParams.get("token");if(!g)throw new b3("No token returned by API");return{data:{signedUrl:f.toString(),path:a,token:g},error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}update(a,b,c){return cg(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",a,b,c)})}move(a,b,c){return cg(this,void 0,void 0,function*(){try{return{data:yield cd(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:a,destinationKey:b,destinationBucket:null==c?void 0:c.destinationBucket},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}copy(a,b,c){return cg(this,void 0,void 0,function*(){try{return{data:{path:(yield cd(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:a,destinationKey:b,destinationBucket:null==c?void 0:c.destinationBucket},{headers:this.headers})).Key},error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}createSignedUrl(a,b,c){return cg(this,void 0,void 0,function*(){try{let d=this._getFinalPath(a),e=yield cd(this.fetch,`${this.url}/object/sign/${d}`,Object.assign({expiresIn:b},(null==c?void 0:c.transform)?{transform:c.transform}:{}),{headers:this.headers}),f=(null==c?void 0:c.download)?`&download=${!0===c.download?"":c.download}`:"";return{data:e={signedUrl:encodeURI(`${this.url}${e.signedURL}${f}`)},error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}createSignedUrls(a,b,c){return cg(this,void 0,void 0,function*(){try{let d=yield cd(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:b,paths:a},{headers:this.headers}),e=(null==c?void 0:c.download)?`&download=${!0===c.download?"":c.download}`:"";return{data:d.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${e}`):null})),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}download(a,b){return cg(this,void 0,void 0,function*(){let c=void 0!==(null==b?void 0:b.transform),d=this.transformOptsToQueryString((null==b?void 0:b.transform)||{}),e=d?`?${d}`:"";try{let b=this._getFinalPath(a),d=yield cc(this.fetch,`${this.url}/${c?"render/image/authenticated":"object"}/${b}${e}`,{headers:this.headers,noResolveJson:!0});return{data:yield d.blob(),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}info(a){return cg(this,void 0,void 0,function*(){let b=this._getFinalPath(a);try{let a=yield cc(this.fetch,`${this.url}/object/info/${b}`,{headers:this.headers});return{data:b8(a),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}exists(a){return cg(this,void 0,void 0,function*(){let b=this._getFinalPath(a);try{return yield function(a,b,c,d){return b9(this,void 0,void 0,function*(){return cb(a,"HEAD",b,Object.assign(Object.assign({},c),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${b}`,{headers:this.headers}),{data:!0,error:null}}catch(a){if(b4(a)&&a instanceof b6){let b=a.originalError;if([400,404].includes(null==b?void 0:b.status))return{data:!1,error:a}}throw a}})}getPublicUrl(a,b){let c=this._getFinalPath(a),d=[],e=(null==b?void 0:b.download)?`download=${!0===b.download?"":b.download}`:"";""!==e&&d.push(e);let f=void 0!==(null==b?void 0:b.transform),g=this.transformOptsToQueryString((null==b?void 0:b.transform)||{});""!==g&&d.push(g);let h=d.join("&");return""!==h&&(h=`?${h}`),{data:{publicUrl:encodeURI(`${this.url}/${f?"render/image":"object"}/public/${c}${h}`)}}}remove(a){return cg(this,void 0,void 0,function*(){try{return{data:yield ce(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:a},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}list(a,b,c){return cg(this,void 0,void 0,function*(){try{let d=Object.assign(Object.assign(Object.assign({},ch),b),{prefix:a||""});return{data:yield cd(this.fetch,`${this.url}/object/list/${this.bucketId}`,d,{headers:this.headers},c),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}encodeMetadata(a){return JSON.stringify(a)}toBase64(a){return void 0!==cf?cf.from(a).toString("base64"):btoa(a)}_getFinalPath(a){return`${this.bucketId}/${a}`}_removeEmptyFolders(a){return a.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(a){let b=[];return a.width&&b.push(`width=${a.width}`),a.height&&b.push(`height=${a.height}`),a.resize&&b.push(`resize=${a.resize}`),a.format&&b.push(`format=${a.format}`),a.quality&&b.push(`quality=${a.quality}`),b.join("&")}}let ck={"X-Client-Info":"storage-js/2.7.1"};var cl=function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})};class cm{constructor(a,b={},c){this.url=a,this.headers=Object.assign(Object.assign({},ck),b),this.fetch=b7(c)}listBuckets(){return cl(this,void 0,void 0,function*(){try{return{data:yield cc(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}getBucket(a){return cl(this,void 0,void 0,function*(){try{return{data:yield cc(this.fetch,`${this.url}/bucket/${a}`,{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}createBucket(a,b={public:!1}){return cl(this,void 0,void 0,function*(){try{return{data:yield cd(this.fetch,`${this.url}/bucket`,{id:a,name:a,public:b.public,file_size_limit:b.fileSizeLimit,allowed_mime_types:b.allowedMimeTypes},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}updateBucket(a,b){return cl(this,void 0,void 0,function*(){try{return{data:yield function(a,b,c,d,e){return b9(this,void 0,void 0,function*(){return cb(a,"PUT",b,d,void 0,c)})}(this.fetch,`${this.url}/bucket/${a}`,{id:a,name:a,public:b.public,file_size_limit:b.fileSizeLimit,allowed_mime_types:b.allowedMimeTypes},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}emptyBucket(a){return cl(this,void 0,void 0,function*(){try{return{data:yield cd(this.fetch,`${this.url}/bucket/${a}/empty`,{},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}deleteBucket(a){return cl(this,void 0,void 0,function*(){try{return{data:yield ce(this.fetch,`${this.url}/bucket/${a}`,{},{headers:this.headers}),error:null}}catch(a){if(b4(a))return{data:null,error:a};throw a}})}}class cn extends cm{constructor(a,b={},c){super(a,b,c)}from(a){return new cj(this.url,this.headers,a,this.fetch)}}let co="";co="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let cp={headers:{"X-Client-Info":`supabase-js-${co}/2.51.0`}},cq={schema:"public"},cr={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},cs={};var ct=c(3);let cu="2.71.0",cv={"X-Client-Info":`gotrue-js/${cu}`},cw="X-Supabase-Api-Version",cx={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},cy=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class cz extends Error{constructor(a,b,c){super(a),this.__isAuthError=!0,this.name="AuthError",this.status=b,this.code=c}}function cA(a){return"object"==typeof a&&null!==a&&"__isAuthError"in a}class cB extends cz{constructor(a,b,c){super(a,b,c),this.name="AuthApiError",this.status=b,this.code=c}}class cC extends cz{constructor(a,b){super(a),this.name="AuthUnknownError",this.originalError=b}}class cD extends cz{constructor(a,b,c,d){super(a,c,d),this.name=b,this.status=c}}class cE extends cD{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class cF extends cD{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class cG extends cD{constructor(a){super(a,"AuthInvalidCredentialsError",400,void 0)}}class cH extends cD{constructor(a,b=null){super(a,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=b}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class cI extends cD{constructor(a,b=null){super(a,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=b}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class cJ extends cD{constructor(a,b){super(a,"AuthRetryableFetchError",b,void 0)}}function cK(a){return cA(a)&&"AuthRetryableFetchError"===a.name}class cL extends cD{constructor(a,b,c){super(a,"AuthWeakPasswordError",b,"weak_password"),this.reasons=c}}class cM extends cD{constructor(a){super(a,"AuthInvalidJwtError",400,"invalid_jwt")}}let cN="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),cO=" 	\n\r=".split(""),cP=(()=>{let a=Array(128);for(let b=0;b<a.length;b+=1)a[b]=-1;for(let b=0;b<cO.length;b+=1)a[cO[b].charCodeAt(0)]=-2;for(let b=0;b<cN.length;b+=1)a[cN[b].charCodeAt(0)]=b;return a})();function cQ(a,b,c){if(null!==a)for(b.queue=b.queue<<8|a,b.queuedBits+=8;b.queuedBits>=6;)c(cN[b.queue>>b.queuedBits-6&63]),b.queuedBits-=6;else if(b.queuedBits>0)for(b.queue=b.queue<<6-b.queuedBits,b.queuedBits=6;b.queuedBits>=6;)c(cN[b.queue>>b.queuedBits-6&63]),b.queuedBits-=6}function cR(a,b,c){let d=cP[a];if(d>-1)for(b.queue=b.queue<<6|d,b.queuedBits+=6;b.queuedBits>=8;)c(b.queue>>b.queuedBits-8&255),b.queuedBits-=8;else if(-2===d)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(a)}"`)}function cS(a){let b=[],c=a=>{b.push(String.fromCodePoint(a))},d={utf8seq:0,codepoint:0},e={queue:0,queuedBits:0},f=a=>{!function(a,b,c){if(0===b.utf8seq){if(a<=127)return c(a);for(let c=1;c<6;c+=1)if((a>>7-c&1)==0){b.utf8seq=c;break}if(2===b.utf8seq)b.codepoint=31&a;else if(3===b.utf8seq)b.codepoint=15&a;else if(4===b.utf8seq)b.codepoint=7&a;else throw Error("Invalid UTF-8 sequence");b.utf8seq-=1}else if(b.utf8seq>0){if(a<=127)throw Error("Invalid UTF-8 sequence");b.codepoint=b.codepoint<<6|63&a,b.utf8seq-=1,0===b.utf8seq&&c(b.codepoint)}}(a,d,c)};for(let b=0;b<a.length;b+=1)cR(a.charCodeAt(b),e,f);return b.join("")}let cT=()=>"undefined"!=typeof window&&"undefined"!=typeof document,cU={tested:!1,writable:!1},cV=()=>{if(!cT())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(a){return!1}if(cU.tested)return cU.writable;let a=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(a,a),globalThis.localStorage.removeItem(a),cU.tested=!0,cU.writable=!0}catch(a){cU.tested=!0,cU.writable=!1}return cU.writable},cW=a=>{let b;return b=a||("undefined"==typeof fetch?(...a)=>Promise.resolve().then(c.bind(c,3)).then(({default:b})=>b(...a)):fetch),(...a)=>b(...a)},cX=async(a,b,c)=>{await a.setItem(b,JSON.stringify(c))},cY=async(a,b)=>{let c=await a.getItem(b);if(!c)return null;try{return JSON.parse(c)}catch(a){return c}},cZ=async(a,b)=>{await a.removeItem(b)};class c${constructor(){this.promise=new c$.promiseConstructor((a,b)=>{this.resolve=a,this.reject=b})}}function c_(a){let b=a.split(".");if(3!==b.length)throw new cM("Invalid JWT structure");for(let a=0;a<b.length;a++)if(!cy.test(b[a]))throw new cM("JWT not in base64url format");return{header:JSON.parse(cS(b[0])),payload:JSON.parse(cS(b[1])),signature:function(a){let b=[],c={queue:0,queuedBits:0},d=a=>{b.push(a)};for(let b=0;b<a.length;b+=1)cR(a.charCodeAt(b),c,d);return new Uint8Array(b)}(b[2]),raw:{header:b[0],payload:b[1]}}}async function c0(a){return await new Promise(b=>{setTimeout(()=>b(null),a)})}function c1(a){return("0"+a.toString(16)).substr(-2)}async function c2(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>String.fromCharCode(a)).join("")}async function c3(a){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),a):btoa(await c2(a)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function c4(a,b,c=!1){let d=function(){let a=new Uint32Array(56);if("undefined"==typeof crypto){let a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",b=a.length,c="";for(let d=0;d<56;d++)c+=a.charAt(Math.floor(Math.random()*b));return c}return crypto.getRandomValues(a),Array.from(a,c1).join("")}(),e=d;c&&(e+="/PASSWORD_RECOVERY"),await cX(a,`${b}-code-verifier`,e);let f=await c3(d),g=d===f?"plain":"s256";return[f,g]}c$.promiseConstructor=Promise;let c5=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,c6=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function c7(a){if(!c6.test(a))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function c8(){return new Proxy({},{get:(a,b)=>{if("__isUserNotAvailableProxy"===b)return!0;if("symbol"==typeof b){let a=b.toString();if("Symbol(Symbol.toPrimitive)"===a||"Symbol(Symbol.toStringTag)"===a||"Symbol(util.inspect.custom)"===a)return}throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${b}" property of the session object is not supported. Please use getUser() instead.`)},set:(a,b)=>{throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${b}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(a,b)=>{throw Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${b}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}var c9=function(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c};let da=a=>a.msg||a.message||a.error_description||a.error||JSON.stringify(a),db=[502,503,504];async function dc(a){var b;let c,d;if(!("object"==typeof a&&null!==a&&"status"in a&&"ok"in a&&"json"in a&&"function"==typeof a.json))throw new cJ(da(a),0);if(db.includes(a.status))throw new cJ(da(a),a.status);try{c=await a.json()}catch(a){throw new cC(da(a),a)}let e=function(a){let b=a.headers.get(cw);if(!b||!b.match(c5))return null;try{return new Date(`${b}T00:00:00.0Z`)}catch(a){return null}}(a);if(e&&e.getTime()>=cx["2024-01-01"].timestamp&&"object"==typeof c&&c&&"string"==typeof c.code?d=c.code:"object"==typeof c&&c&&"string"==typeof c.error_code&&(d=c.error_code),d){if("weak_password"===d)throw new cL(da(c),a.status,(null==(b=c.weak_password)?void 0:b.reasons)||[]);else if("session_not_found"===d)throw new cE}else if("object"==typeof c&&c&&"object"==typeof c.weak_password&&c.weak_password&&Array.isArray(c.weak_password.reasons)&&c.weak_password.reasons.length&&c.weak_password.reasons.reduce((a,b)=>a&&"string"==typeof b,!0))throw new cL(da(c),a.status,c.weak_password.reasons);throw new cB(da(c),a.status||500,d)}async function dd(a,b,c,d){var e;let f=Object.assign({},null==d?void 0:d.headers);f[cw]||(f[cw]=cx["2024-01-01"].name),(null==d?void 0:d.jwt)&&(f.Authorization=`Bearer ${d.jwt}`);let g=null!=(e=null==d?void 0:d.query)?e:{};(null==d?void 0:d.redirectTo)&&(g.redirect_to=d.redirectTo);let h=Object.keys(g).length?"?"+new URLSearchParams(g).toString():"",i=await de(a,b,c+h,{headers:f,noResolveJson:null==d?void 0:d.noResolveJson},{},null==d?void 0:d.body);return(null==d?void 0:d.xform)?null==d?void 0:d.xform(i):{data:Object.assign({},i),error:null}}async function de(a,b,c,d,e,f){let g,h=((a,b,c,d)=>{let e={method:a,headers:(null==b?void 0:b.headers)||{}};return"GET"===a?e:(e.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==b?void 0:b.headers),e.body=JSON.stringify(d),Object.assign(Object.assign({},e),c))})(b,d,e,f);try{g=await a(c,Object.assign({},h))}catch(a){throw console.error(a),new cJ(da(a),0)}if(g.ok||await dc(g),null==d?void 0:d.noResolveJson)return g;try{return await g.json()}catch(a){await dc(a)}}function df(a){var b,c,d;let e=null;(d=a).access_token&&d.refresh_token&&d.expires_in&&(e=Object.assign({},a),a.expires_at||(e.expires_at=(c=a.expires_in,Math.round(Date.now()/1e3)+c)));return{data:{session:e,user:null!=(b=a.user)?b:a},error:null}}function dg(a){let b=df(a);return!b.error&&a.weak_password&&"object"==typeof a.weak_password&&Array.isArray(a.weak_password.reasons)&&a.weak_password.reasons.length&&a.weak_password.message&&"string"==typeof a.weak_password.message&&a.weak_password.reasons.reduce((a,b)=>a&&"string"==typeof b,!0)&&(b.data.weak_password=a.weak_password),b}function dh(a){var b;return{data:{user:null!=(b=a.user)?b:a},error:null}}function di(a){return{data:a,error:null}}function dj(a){let{action_link:b,email_otp:c,hashed_token:d,redirect_to:e,verification_type:f}=a;return{data:{properties:{action_link:b,email_otp:c,hashed_token:d,redirect_to:e,verification_type:f},user:Object.assign({},c9(a,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function dk(a){return a}let dl=["global","local","others"];var dm=function(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c};class dn{constructor({url:a="",headers:b={},fetch:c}){this.url=a,this.headers=b,this.fetch=cW(c),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(a,b=dl[0]){if(0>dl.indexOf(b))throw Error(`@supabase/auth-js: Parameter scope must be one of ${dl.join(", ")}`);try{return await dd(this.fetch,"POST",`${this.url}/logout?scope=${b}`,{headers:this.headers,jwt:a,noResolveJson:!0}),{data:null,error:null}}catch(a){if(cA(a))return{data:null,error:a};throw a}}async inviteUserByEmail(a,b={}){try{return await dd(this.fetch,"POST",`${this.url}/invite`,{body:{email:a,data:b.data},headers:this.headers,redirectTo:b.redirectTo,xform:dh})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async generateLink(a){try{let{options:b}=a,c=dm(a,["options"]),d=Object.assign(Object.assign({},c),b);return"newEmail"in c&&(d.new_email=null==c?void 0:c.newEmail,delete d.newEmail),await dd(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:d,headers:this.headers,xform:dj,redirectTo:null==b?void 0:b.redirectTo})}catch(a){if(cA(a))return{data:{properties:null,user:null},error:a};throw a}}async createUser(a){try{return await dd(this.fetch,"POST",`${this.url}/admin/users`,{body:a,headers:this.headers,xform:dh})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async listUsers(a){var b,c,d,e,f,g,h;try{let i={nextPage:null,lastPage:0,total:0},j=await dd(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(c=null==(b=null==a?void 0:a.page)?void 0:b.toString())?c:"",per_page:null!=(e=null==(d=null==a?void 0:a.perPage)?void 0:d.toString())?e:""},xform:dk});if(j.error)throw j.error;let k=await j.json(),l=null!=(f=j.headers.get("x-total-count"))?f:0,m=null!=(h=null==(g=j.headers.get("link"))?void 0:g.split(","))?h:[];return m.length>0&&(m.forEach(a=>{let b=parseInt(a.split(";")[0].split("=")[1].substring(0,1)),c=JSON.parse(a.split(";")[1].split("=")[1]);i[`${c}Page`]=b}),i.total=parseInt(l)),{data:Object.assign(Object.assign({},k),i),error:null}}catch(a){if(cA(a))return{data:{users:[]},error:a};throw a}}async getUserById(a){c7(a);try{return await dd(this.fetch,"GET",`${this.url}/admin/users/${a}`,{headers:this.headers,xform:dh})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async updateUserById(a,b){c7(a);try{return await dd(this.fetch,"PUT",`${this.url}/admin/users/${a}`,{body:b,headers:this.headers,xform:dh})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async deleteUser(a,b=!1){c7(a);try{return await dd(this.fetch,"DELETE",`${this.url}/admin/users/${a}`,{headers:this.headers,body:{should_soft_delete:b},xform:dh})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async _listFactors(a){c7(a.userId);try{let{data:b,error:c}=await dd(this.fetch,"GET",`${this.url}/admin/users/${a.userId}/factors`,{headers:this.headers,xform:a=>({data:{factors:a},error:null})});return{data:b,error:c}}catch(a){if(cA(a))return{data:null,error:a};throw a}}async _deleteFactor(a){c7(a.userId),c7(a.id);try{return{data:await dd(this.fetch,"DELETE",`${this.url}/admin/users/${a.userId}/factors/${a.id}`,{headers:this.headers}),error:null}}catch(a){if(cA(a))return{data:null,error:a};throw a}}}function dp(a={}){return{getItem:b=>a[b]||null,setItem:(b,c)=>{a[b]=c},removeItem:b=>{delete a[b]}}}let dq={debug:!!(globalThis&&cV()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class dr extends Error{constructor(a){super(a),this.isAcquireTimeout=!0}}class ds extends dr{}async function dt(a,b,c){dq.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",a,b);let d=new globalThis.AbortController;return b>0&&setTimeout(()=>{d.abort(),dq.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",a)},b),await Promise.resolve().then(()=>globalThis.navigator.locks.request(a,0===b?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:d.signal},async d=>{if(d){dq.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",a,d.name);try{return await c()}finally{dq.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",a,d.name)}}if(0===b)throw dq.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",a),new ds(`Acquiring an exclusive Navigator LockManager lock "${a}" immediately failed`);if(dq.debug)try{let a=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(a,null,"  "))}catch(a){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",a)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await c()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(a){"undefined"!=typeof self&&(self.globalThis=self)}let du={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:cv,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function dv(a,b,c){return await c()}let dw={};class dx{constructor(a){var b,c;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=dx.nextInstanceID,dx.nextInstanceID+=1,this.instanceID>0&&cT()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let d=Object.assign(Object.assign({},du),a);if(this.logDebugMessages=!!d.debug,"function"==typeof d.debug&&(this.logger=d.debug),this.persistSession=d.persistSession,this.storageKey=d.storageKey,this.autoRefreshToken=d.autoRefreshToken,this.admin=new dn({url:d.url,headers:d.headers,fetch:d.fetch}),this.url=d.url,this.headers=d.headers,this.fetch=cW(d.fetch),this.lock=d.lock||dv,this.detectSessionInUrl=d.detectSessionInUrl,this.flowType=d.flowType,this.hasCustomAuthorizationHeader=d.hasCustomAuthorizationHeader,d.lock?this.lock=d.lock:cT()&&(null==(b=null==globalThis?void 0:globalThis.navigator)?void 0:b.locks)?this.lock=dt:this.lock=dv,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(d.storage?this.storage=d.storage:cV()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=dp(this.memoryStorage)),d.userStorage&&(this.userStorage=d.userStorage)):(this.memoryStorage={},this.storage=dp(this.memoryStorage)),cT()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(a){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",a)}null==(c=this.broadcastChannel)||c.addEventListener("message",async a=>{this._debug("received broadcast notification from other tab or client",a),await this._notifyAllSubscribers(a.data.event,a.data.session,!1)})}this.initialize()}get jwks(){var a,b;return null!=(b=null==(a=dw[this.storageKey])?void 0:a.jwks)?b:{keys:[]}}set jwks(a){dw[this.storageKey]=Object.assign(Object.assign({},dw[this.storageKey]),{jwks:a})}get jwks_cached_at(){var a,b;return null!=(b=null==(a=dw[this.storageKey])?void 0:a.cachedAt)?b:Number.MIN_SAFE_INTEGER}set jwks_cached_at(a){dw[this.storageKey]=Object.assign(Object.assign({},dw[this.storageKey]),{cachedAt:a})}_debug(...a){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${cu}) ${new Date().toISOString()}`,...a),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var a;try{let b=function(a){let b={},c=new URL(a);if(c.hash&&"#"===c.hash[0])try{new URLSearchParams(c.hash.substring(1)).forEach((a,c)=>{b[c]=a})}catch(a){}return c.searchParams.forEach((a,c)=>{b[c]=a}),b}(window.location.href),c="none";if(this._isImplicitGrantCallback(b)?c="implicit":await this._isPKCECallback(b)&&(c="pkce"),cT()&&this.detectSessionInUrl&&"none"!==c){let{data:d,error:e}=await this._getSessionFromURL(b,c);if(e){if(this._debug("#_initialize()","error detecting session from URL",e),cA(e)&&"AuthImplicitGrantRedirectError"===e.name){let b=null==(a=e.details)?void 0:a.code;if("identity_already_exists"===b||"identity_not_found"===b||"single_identity_not_deletable"===b)return{error:e}}return await this._removeSession(),{error:e}}let{session:f,redirectType:g}=d;return this._debug("#_initialize()","detected session in URL",f,"redirect type",g),await this._saveSession(f),setTimeout(async()=>{"recovery"===g?await this._notifyAllSubscribers("PASSWORD_RECOVERY",f):await this._notifyAllSubscribers("SIGNED_IN",f)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(a){if(cA(a))return{error:a};return{error:new cC("Unexpected error during initialization",a)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(a){var b,c,d;try{let{data:e,error:f}=await dd(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(c=null==(b=null==a?void 0:a.options)?void 0:b.data)?c:{},gotrue_meta_security:{captcha_token:null==(d=null==a?void 0:a.options)?void 0:d.captchaToken}},xform:df});if(f||!e)return{data:{user:null,session:null},error:f};let g=e.session,h=e.user;return e.session&&(await this._saveSession(e.session),await this._notifyAllSubscribers("SIGNED_IN",g)),{data:{user:h,session:g},error:null}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async signUp(a){var b,c,d;try{let e;if("email"in a){let{email:c,password:d,options:f}=a,g=null,h=null;"pkce"===this.flowType&&([g,h]=await c4(this.storage,this.storageKey)),e=await dd(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==f?void 0:f.emailRedirectTo,body:{email:c,password:d,data:null!=(b=null==f?void 0:f.data)?b:{},gotrue_meta_security:{captcha_token:null==f?void 0:f.captchaToken},code_challenge:g,code_challenge_method:h},xform:df})}else if("phone"in a){let{phone:b,password:f,options:g}=a;e=await dd(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:b,password:f,data:null!=(c=null==g?void 0:g.data)?c:{},channel:null!=(d=null==g?void 0:g.channel)?d:"sms",gotrue_meta_security:{captcha_token:null==g?void 0:g.captchaToken}},xform:df})}else throw new cG("You must provide either an email or phone number and a password");let{data:f,error:g}=e;if(g||!f)return{data:{user:null,session:null},error:g};let h=f.session,i=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",h)),{data:{user:i,session:h},error:null}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithPassword(a){try{let b;if("email"in a){let{email:c,password:d,options:e}=a;b=await dd(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:c,password:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:dg})}else if("phone"in a){let{phone:c,password:d,options:e}=a;b=await dd(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:c,password:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:dg})}else throw new cG("You must provide either an email or phone number and a password");let{data:c,error:d}=b;if(d)return{data:{user:null,session:null},error:d};if(!c||!c.session||!c.user)return{data:{user:null,session:null},error:new cF};return c.session&&(await this._saveSession(c.session),await this._notifyAllSubscribers("SIGNED_IN",c.session)),{data:Object.assign({user:c.user,session:c.session},c.weak_password?{weakPassword:c.weak_password}:null),error:d}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithOAuth(a){var b,c,d,e;return await this._handleProviderSignIn(a.provider,{redirectTo:null==(b=a.options)?void 0:b.redirectTo,scopes:null==(c=a.options)?void 0:c.scopes,queryParams:null==(d=a.options)?void 0:d.queryParams,skipBrowserRedirect:null==(e=a.options)?void 0:e.skipBrowserRedirect})}async exchangeCodeForSession(a){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(a))}async signInWithWeb3(a){let{chain:b}=a;if("solana"===b)return await this.signInWithSolana(a);throw Error(`@supabase/auth-js: Unsupported chain "${b}"`)}async signInWithSolana(a){var b,c,d,e,f,g,h,i,j,k,l,m;let n,o;if("message"in a)n=a.message,o=a.signature;else{let l,{chain:m,wallet:p,statement:q,options:r}=a;if(cT())if("object"==typeof p)l=p;else{let a=window;if("solana"in a&&"object"==typeof a.solana&&("signIn"in a.solana&&"function"==typeof a.solana.signIn||"signMessage"in a.solana&&"function"==typeof a.solana.signMessage))l=a.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof p||!(null==r?void 0:r.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");l=p}let s=new URL(null!=(b=null==r?void 0:r.url)?b:window.location.href);if("signIn"in l&&l.signIn){let a,b=await l.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==r?void 0:r.signInWithSolana),{version:"1",domain:s.host,uri:s.href}),q?{statement:q}:null));if(Array.isArray(b)&&b[0]&&"object"==typeof b[0])a=b[0];else if(b&&"object"==typeof b&&"signedMessage"in b&&"signature"in b)a=b;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in a&&"signature"in a&&("string"==typeof a.signedMessage||a.signedMessage instanceof Uint8Array)&&a.signature instanceof Uint8Array)n="string"==typeof a.signedMessage?a.signedMessage:new TextDecoder().decode(a.signedMessage),o=a.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in l)||"function"!=typeof l.signMessage||!("publicKey"in l)||"object"!=typeof l||!l.publicKey||!("toBase58"in l.publicKey)||"function"!=typeof l.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");n=[`${s.host} wants you to sign in with your Solana account:`,l.publicKey.toBase58(),...q?["",q,""]:[""],"Version: 1",`URI: ${s.href}`,`Issued At: ${null!=(d=null==(c=null==r?void 0:r.signInWithSolana)?void 0:c.issuedAt)?d:new Date().toISOString()}`,...(null==(e=null==r?void 0:r.signInWithSolana)?void 0:e.notBefore)?[`Not Before: ${r.signInWithSolana.notBefore}`]:[],...(null==(f=null==r?void 0:r.signInWithSolana)?void 0:f.expirationTime)?[`Expiration Time: ${r.signInWithSolana.expirationTime}`]:[],...(null==(g=null==r?void 0:r.signInWithSolana)?void 0:g.chainId)?[`Chain ID: ${r.signInWithSolana.chainId}`]:[],...(null==(h=null==r?void 0:r.signInWithSolana)?void 0:h.nonce)?[`Nonce: ${r.signInWithSolana.nonce}`]:[],...(null==(i=null==r?void 0:r.signInWithSolana)?void 0:i.requestId)?[`Request ID: ${r.signInWithSolana.requestId}`]:[],...(null==(k=null==(j=null==r?void 0:r.signInWithSolana)?void 0:j.resources)?void 0:k.length)?["Resources",...r.signInWithSolana.resources.map(a=>`- ${a}`)]:[]].join("\n");let a=await l.signMessage(new TextEncoder().encode(n),"utf8");if(!a||!(a instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");o=a}}try{let{data:b,error:c}=await dd(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:n,signature:function(a){let b=[],c={queue:0,queuedBits:0},d=a=>{b.push(a)};return a.forEach(a=>cQ(a,c,d)),cQ(null,c,d),b.join("")}(o)},(null==(l=a.options)?void 0:l.captchaToken)?{gotrue_meta_security:{captcha_token:null==(m=a.options)?void 0:m.captchaToken}}:null),xform:df});if(c)throw c;if(!b||!b.session||!b.user)return{data:{user:null,session:null},error:new cF};return b.session&&(await this._saveSession(b.session),await this._notifyAllSubscribers("SIGNED_IN",b.session)),{data:Object.assign({},b),error:c}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async _exchangeCodeForSession(a){let b=await cY(this.storage,`${this.storageKey}-code-verifier`),[c,d]=(null!=b?b:"").split("/");try{let{data:b,error:e}=await dd(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:a,code_verifier:c},xform:df});if(await cZ(this.storage,`${this.storageKey}-code-verifier`),e)throw e;if(!b||!b.session||!b.user)return{data:{user:null,session:null,redirectType:null},error:new cF};return b.session&&(await this._saveSession(b.session),await this._notifyAllSubscribers("SIGNED_IN",b.session)),{data:Object.assign(Object.assign({},b),{redirectType:null!=d?d:null}),error:e}}catch(a){if(cA(a))return{data:{user:null,session:null,redirectType:null},error:a};throw a}}async signInWithIdToken(a){try{let{options:b,provider:c,token:d,access_token:e,nonce:f}=a,{data:g,error:h}=await dd(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:c,id_token:d,access_token:e,nonce:f,gotrue_meta_security:{captcha_token:null==b?void 0:b.captchaToken}},xform:df});if(h)return{data:{user:null,session:null},error:h};if(!g||!g.session||!g.user)return{data:{user:null,session:null},error:new cF};return g.session&&(await this._saveSession(g.session),await this._notifyAllSubscribers("SIGNED_IN",g.session)),{data:g,error:h}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithOtp(a){var b,c,d,e,f;try{if("email"in a){let{email:d,options:e}=a,f=null,g=null;"pkce"===this.flowType&&([f,g]=await c4(this.storage,this.storageKey));let{error:h}=await dd(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:d,data:null!=(b=null==e?void 0:e.data)?b:{},create_user:null==(c=null==e?void 0:e.shouldCreateUser)||c,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken},code_challenge:f,code_challenge_method:g},redirectTo:null==e?void 0:e.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in a){let{phone:b,options:c}=a,{data:g,error:h}=await dd(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:b,data:null!=(d=null==c?void 0:c.data)?d:{},create_user:null==(e=null==c?void 0:c.shouldCreateUser)||e,gotrue_meta_security:{captcha_token:null==c?void 0:c.captchaToken},channel:null!=(f=null==c?void 0:c.channel)?f:"sms"}});return{data:{user:null,session:null,messageId:null==g?void 0:g.message_id},error:h}}throw new cG("You must provide either an email or phone number.")}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(a){var b,c;try{let d,e;"options"in a&&(d=null==(b=a.options)?void 0:b.redirectTo,e=null==(c=a.options)?void 0:c.captchaToken);let{data:f,error:g}=await dd(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},a),{gotrue_meta_security:{captcha_token:e}}),redirectTo:d,xform:df});if(g)throw g;if(!f)throw Error("An error occurred on token verification.");let h=f.session,i=f.user;return(null==h?void 0:h.access_token)&&(await this._saveSession(h),await this._notifyAllSubscribers("recovery"==a.type?"PASSWORD_RECOVERY":"SIGNED_IN",h)),{data:{user:i,session:h},error:null}}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithSSO(a){var b,c,d;try{let e=null,f=null;return"pkce"===this.flowType&&([e,f]=await c4(this.storage,this.storageKey)),await dd(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in a?{provider_id:a.providerId}:null),"domain"in a?{domain:a.domain}:null),{redirect_to:null!=(c=null==(b=a.options)?void 0:b.redirectTo)?c:void 0}),(null==(d=null==a?void 0:a.options)?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:a.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:e,code_challenge_method:f}),headers:this.headers,xform:di})}catch(a){if(cA(a))return{data:null,error:a};throw a}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async a=>{let{data:{session:b},error:c}=a;if(c)throw c;if(!b)throw new cE;let{error:d}=await dd(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:b.access_token});return{data:{user:null,session:null},error:d}})}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async resend(a){try{let b=`${this.url}/resend`;if("email"in a){let{email:c,type:d,options:e}=a,{error:f}=await dd(this.fetch,"POST",b,{headers:this.headers,body:{email:c,type:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},redirectTo:null==e?void 0:e.emailRedirectTo});return{data:{user:null,session:null},error:f}}if("phone"in a){let{phone:c,type:d,options:e}=a,{data:f,error:g}=await dd(this.fetch,"POST",b,{headers:this.headers,body:{phone:c,type:d,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}}});return{data:{user:null,session:null,messageId:null==f?void 0:f.message_id},error:g}}throw new cG("You must provide either an email or phone number and a type")}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async a=>a))}async _acquireLock(a,b){this._debug("#_acquireLock","begin",a);try{if(this.lockAcquired){let a=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),c=(async()=>(await a,await b()))();return this.pendingInLock.push((async()=>{try{await c}catch(a){}})()),c}return await this.lock(`lock:${this.storageKey}`,a,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let a=b();for(this.pendingInLock.push((async()=>{try{await a}catch(a){}})()),await a;this.pendingInLock.length;){let a=[...this.pendingInLock];await Promise.all(a),this.pendingInLock.splice(0,a.length)}return await a}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(a){this._debug("#_useSession","begin");try{let b=await this.__loadSession();return await a(b)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let a=null,b=await cY(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",b),null!==b&&(this._isValidSession(b)?a=b:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!a)return{data:{session:null},error:null};let c=!!a.expires_at&&1e3*a.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${c?"":" not"} expired`,"expires_at",a.expires_at),!c){if(this.userStorage){let b=await cY(this.userStorage,this.storageKey+"-user");(null==b?void 0:b.user)?a.user=b.user:a.user=c8()}if(this.storage.isServer&&a.user){let b=this.suppressGetSessionWarning;a=new Proxy(a,{get:(a,c,d)=>(b||"user"!==c||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),b=!0,this.suppressGetSessionWarning=!0),Reflect.get(a,c,d))})}return{data:{session:a},error:null}}let{session:d,error:e}=await this._callRefreshToken(a.refresh_token);if(e)return{data:{session:null},error:e};return{data:{session:d},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(a){return a?await this._getUser(a):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(a){try{if(a)return await dd(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:a,xform:dh});return await this._useSession(async a=>{var b,c,d;let{data:e,error:f}=a;if(f)throw f;return(null==(b=e.session)?void 0:b.access_token)||this.hasCustomAuthorizationHeader?await dd(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(d=null==(c=e.session)?void 0:c.access_token)?d:void 0,xform:dh}):{data:{user:null},error:new cE}})}catch(a){if(cA(a))return cA(a)&&"AuthSessionMissingError"===a.name&&(await this._removeSession(),await cZ(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:a};throw a}}async updateUser(a,b={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(a,b))}async _updateUser(a,b={}){try{return await this._useSession(async c=>{let{data:d,error:e}=c;if(e)throw e;if(!d.session)throw new cE;let f=d.session,g=null,h=null;"pkce"===this.flowType&&null!=a.email&&([g,h]=await c4(this.storage,this.storageKey));let{data:i,error:j}=await dd(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==b?void 0:b.emailRedirectTo,body:Object.assign(Object.assign({},a),{code_challenge:g,code_challenge_method:h}),jwt:f.access_token,xform:dh});if(j)throw j;return f.user=i.user,await this._saveSession(f),await this._notifyAllSubscribers("USER_UPDATED",f),{data:{user:f.user},error:null}})}catch(a){if(cA(a))return{data:{user:null},error:a};throw a}}async setSession(a){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(a))}async _setSession(a){try{if(!a.access_token||!a.refresh_token)throw new cE;let b=Date.now()/1e3,c=b,d=!0,e=null,{payload:f}=c_(a.access_token);if(f.exp&&(d=(c=f.exp)<=b),d){let{session:b,error:c}=await this._callRefreshToken(a.refresh_token);if(c)return{data:{user:null,session:null},error:c};if(!b)return{data:{user:null,session:null},error:null};e=b}else{let{data:d,error:f}=await this._getUser(a.access_token);if(f)throw f;e={access_token:a.access_token,refresh_token:a.refresh_token,user:d.user,token_type:"bearer",expires_in:c-b,expires_at:c},await this._saveSession(e),await this._notifyAllSubscribers("SIGNED_IN",e)}return{data:{user:e.user,session:e},error:null}}catch(a){if(cA(a))return{data:{session:null,user:null},error:a};throw a}}async refreshSession(a){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(a))}async _refreshSession(a){try{return await this._useSession(async b=>{var c;if(!a){let{data:d,error:e}=b;if(e)throw e;a=null!=(c=d.session)?c:void 0}if(!(null==a?void 0:a.refresh_token))throw new cE;let{session:d,error:e}=await this._callRefreshToken(a.refresh_token);return e?{data:{user:null,session:null},error:e}:d?{data:{user:d.user,session:d},error:null}:{data:{user:null,session:null},error:null}})}catch(a){if(cA(a))return{data:{user:null,session:null},error:a};throw a}}async _getSessionFromURL(a,b){try{if(!cT())throw new cH("No browser detected.");if(a.error||a.error_description||a.error_code)throw new cH(a.error_description||"Error in URL with unspecified error_description",{error:a.error||"unspecified_error",code:a.error_code||"unspecified_code"});switch(b){case"implicit":if("pkce"===this.flowType)throw new cI("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new cH("Not a valid implicit grant flow url.")}if("pkce"===b){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!a.code)throw new cI("No code detected.");let{data:b,error:c}=await this._exchangeCodeForSession(a.code);if(c)throw c;let d=new URL(window.location.href);return d.searchParams.delete("code"),window.history.replaceState(window.history.state,"",d.toString()),{data:{session:b.session,redirectType:null},error:null}}let{provider_token:c,provider_refresh_token:d,access_token:e,refresh_token:f,expires_in:g,expires_at:h,token_type:i}=a;if(!e||!g||!f||!i)throw new cH("No session defined in URL");let j=Math.round(Date.now()/1e3),k=parseInt(g),l=j+k;h&&(l=parseInt(h));let m=l-j;1e3*m<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${m}s, should have been closer to ${k}s`);let n=l-k;j-n>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",n,l,j):j-n<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",n,l,j);let{data:o,error:p}=await this._getUser(e);if(p)throw p;let q={provider_token:c,provider_refresh_token:d,access_token:e,expires_in:k,expires_at:l,refresh_token:f,token_type:i,user:o.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:q,redirectType:a.type},error:null}}catch(a){if(cA(a))return{data:{session:null,redirectType:null},error:a};throw a}}_isImplicitGrantCallback(a){return!!(a.access_token||a.error_description)}async _isPKCECallback(a){let b=await cY(this.storage,`${this.storageKey}-code-verifier`);return!!(a.code&&b)}async signOut(a={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(a))}async _signOut({scope:a}={scope:"global"}){return await this._useSession(async b=>{var c;let{data:d,error:e}=b;if(e)return{error:e};let f=null==(c=d.session)?void 0:c.access_token;if(f){let{error:b}=await this.admin.signOut(f,a);if(b&&!(cA(b)&&"AuthApiError"===b.name&&(404===b.status||401===b.status||403===b.status)))return{error:b}}return"others"!==a&&(await this._removeSession(),await cZ(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(a){let b="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){let b=16*Math.random()|0;return("x"==a?b:3&b|8).toString(16)}),c={id:b,callback:a,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",b),this.stateChangeEmitters.delete(b)}};return this._debug("#onAuthStateChange()","registered callback with id",b),this.stateChangeEmitters.set(b,c),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(b)})})(),{data:{subscription:c}}}async _emitInitialSession(a){return await this._useSession(async b=>{var c,d;try{let{data:{session:d},error:e}=b;if(e)throw e;await (null==(c=this.stateChangeEmitters.get(a))?void 0:c.callback("INITIAL_SESSION",d)),this._debug("INITIAL_SESSION","callback id",a,"session",d)}catch(b){await (null==(d=this.stateChangeEmitters.get(a))?void 0:d.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",a,"error",b),console.error(b)}})}async resetPasswordForEmail(a,b={}){let c=null,d=null;"pkce"===this.flowType&&([c,d]=await c4(this.storage,this.storageKey,!0));try{return await dd(this.fetch,"POST",`${this.url}/recover`,{body:{email:a,code_challenge:c,code_challenge_method:d,gotrue_meta_security:{captcha_token:b.captchaToken}},headers:this.headers,redirectTo:b.redirectTo})}catch(a){if(cA(a))return{data:null,error:a};throw a}}async getUserIdentities(){var a;try{let{data:b,error:c}=await this.getUser();if(c)throw c;return{data:{identities:null!=(a=b.user.identities)?a:[]},error:null}}catch(a){if(cA(a))return{data:null,error:a};throw a}}async linkIdentity(a){var b;try{let{data:c,error:d}=await this._useSession(async b=>{var c,d,e,f,g;let{data:h,error:i}=b;if(i)throw i;let j=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,a.provider,{redirectTo:null==(c=a.options)?void 0:c.redirectTo,scopes:null==(d=a.options)?void 0:d.scopes,queryParams:null==(e=a.options)?void 0:e.queryParams,skipBrowserRedirect:!0});return await dd(this.fetch,"GET",j,{headers:this.headers,jwt:null!=(g=null==(f=h.session)?void 0:f.access_token)?g:void 0})});if(d)throw d;return!cT()||(null==(b=a.options)?void 0:b.skipBrowserRedirect)||window.location.assign(null==c?void 0:c.url),{data:{provider:a.provider,url:null==c?void 0:c.url},error:null}}catch(b){if(cA(b))return{data:{provider:a.provider,url:null},error:b};throw b}}async unlinkIdentity(a){try{return await this._useSession(async b=>{var c,d;let{data:e,error:f}=b;if(f)throw f;return await dd(this.fetch,"DELETE",`${this.url}/user/identities/${a.identity_id}`,{headers:this.headers,jwt:null!=(d=null==(c=e.session)?void 0:c.access_token)?d:void 0})})}catch(a){if(cA(a))return{data:null,error:a};throw a}}async _refreshAccessToken(a){let b=`#_refreshAccessToken(${a.substring(0,5)}...)`;this._debug(b,"begin");try{var c,d;let e=Date.now();return await (c=async c=>(c>0&&await c0(200*Math.pow(2,c-1)),this._debug(b,"refreshing attempt",c),await dd(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:a},headers:this.headers,xform:df})),d=(a,b)=>{let c=200*Math.pow(2,a);return b&&cK(b)&&Date.now()+c-e<3e4},new Promise((a,b)=>{(async()=>{for(let e=0;e<1/0;e++)try{let b=await c(e);if(!d(e,null,b))return void a(b)}catch(a){if(!d(e,a))return void b(a)}})()}))}catch(a){if(this._debug(b,"error",a),cA(a))return{data:{session:null,user:null},error:a};throw a}finally{this._debug(b,"end")}}_isValidSession(a){return"object"==typeof a&&null!==a&&"access_token"in a&&"refresh_token"in a&&"expires_at"in a}async _handleProviderSignIn(a,b){let c=await this._getUrlForProvider(`${this.url}/authorize`,a,{redirectTo:b.redirectTo,scopes:b.scopes,queryParams:b.queryParams});return this._debug("#_handleProviderSignIn()","provider",a,"options",b,"url",c),cT()&&!b.skipBrowserRedirect&&window.location.assign(c),{data:{provider:a,url:c},error:null}}async _recoverAndRefresh(){var a,b;let c="#_recoverAndRefresh()";this._debug(c,"begin");try{let d=await cY(this.storage,this.storageKey);if(d&&this.userStorage){let b=await cY(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!b&&(b={user:d.user},await cX(this.userStorage,this.storageKey+"-user",b)),d.user=null!=(a=null==b?void 0:b.user)?a:c8()}else if(d&&!d.user&&!d.user){let a=await cY(this.storage,this.storageKey+"-user");a&&(null==a?void 0:a.user)?(d.user=a.user,await cZ(this.storage,this.storageKey+"-user"),await cX(this.storage,this.storageKey,d)):d.user=c8()}if(this._debug(c,"session from storage",d),!this._isValidSession(d)){this._debug(c,"session is not valid"),null!==d&&await this._removeSession();return}let e=(null!=(b=d.expires_at)?b:1/0)*1e3-Date.now()<9e4;if(this._debug(c,`session has${e?"":" not"} expired with margin of 90000s`),e){if(this.autoRefreshToken&&d.refresh_token){let{error:a}=await this._callRefreshToken(d.refresh_token);a&&(console.error(a),cK(a)||(this._debug(c,"refresh failed with a non-retryable error, removing the session",a),await this._removeSession()))}}else if(d.user&&!0===d.user.__isUserNotAvailableProxy)try{let{data:a,error:b}=await this._getUser(d.access_token);!b&&(null==a?void 0:a.user)?(d.user=a.user,await this._saveSession(d),await this._notifyAllSubscribers("SIGNED_IN",d)):this._debug(c,"could not get user data, skipping SIGNED_IN notification")}catch(a){console.error("Error getting user data:",a),this._debug(c,"error getting user data, skipping SIGNED_IN notification",a)}else await this._notifyAllSubscribers("SIGNED_IN",d)}catch(a){this._debug(c,"error",a),console.error(a);return}finally{this._debug(c,"end")}}async _callRefreshToken(a){var b,c;if(!a)throw new cE;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let d=`#_callRefreshToken(${a.substring(0,5)}...)`;this._debug(d,"begin");try{this.refreshingDeferred=new c$;let{data:b,error:c}=await this._refreshAccessToken(a);if(c)throw c;if(!b.session)throw new cE;await this._saveSession(b.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",b.session);let d={session:b.session,error:null};return this.refreshingDeferred.resolve(d),d}catch(a){if(this._debug(d,"error",a),cA(a)){let c={session:null,error:a};return cK(a)||await this._removeSession(),null==(b=this.refreshingDeferred)||b.resolve(c),c}throw null==(c=this.refreshingDeferred)||c.reject(a),a}finally{this.refreshingDeferred=null,this._debug(d,"end")}}async _notifyAllSubscribers(a,b,c=!0){let d=`#_notifyAllSubscribers(${a})`;this._debug(d,"begin",b,`broadcast = ${c}`);try{this.broadcastChannel&&c&&this.broadcastChannel.postMessage({event:a,session:b});let d=[],e=Array.from(this.stateChangeEmitters.values()).map(async c=>{try{await c.callback(a,b)}catch(a){d.push(a)}});if(await Promise.all(e),d.length>0){for(let a=0;a<d.length;a+=1)console.error(d[a]);throw d[0]}}finally{this._debug(d,"end")}}async _saveSession(a){this._debug("#_saveSession()",a),this.suppressGetSessionWarning=!0;let b=Object.assign({},a),c=b.user&&!0===b.user.__isUserNotAvailableProxy;if(this.userStorage){!c&&b.user&&await cX(this.userStorage,this.storageKey+"-user",{user:b.user});let a=Object.assign({},b);delete a.user;let d=structuredClone(a);await cX(this.storage,this.storageKey,d)}else{let a=structuredClone(b);await cX(this.storage,this.storageKey,a)}}async _removeSession(){this._debug("#_removeSession()"),await cZ(this.storage,this.storageKey),await cZ(this.storage,this.storageKey+"-code-verifier"),await cZ(this.storage,this.storageKey+"-user"),this.userStorage&&await cZ(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let a=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{a&&cT()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",a)}catch(a){console.error("removing visibilitychange callback failed",a)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let a=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=a,a&&"object"==typeof a&&"function"==typeof a.unref?a.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(a),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let a=this.autoRefreshTicker;this.autoRefreshTicker=null,a&&clearInterval(a)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let a=Date.now();try{return await this._useSession(async b=>{let{data:{session:c}}=b;if(!c||!c.refresh_token||!c.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let d=Math.floor((1e3*c.expires_at-a)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${d} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),d<=3&&await this._callRefreshToken(c.refresh_token)})}catch(a){console.error("Auto refresh tick failed with error. This is likely a transient error.",a)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(a){if(a.isAcquireTimeout||a instanceof dr)this._debug("auto refresh token tick lock not available");else throw a}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!cT()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(a){console.error("_handleVisibilityChange",a)}}async _onVisibilityChanged(a){let b=`#_onVisibilityChanged(${a})`;this._debug(b,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),a||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(b,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(a,b,c){let d=[`provider=${encodeURIComponent(b)}`];if((null==c?void 0:c.redirectTo)&&d.push(`redirect_to=${encodeURIComponent(c.redirectTo)}`),(null==c?void 0:c.scopes)&&d.push(`scopes=${encodeURIComponent(c.scopes)}`),"pkce"===this.flowType){let[a,b]=await c4(this.storage,this.storageKey),c=new URLSearchParams({code_challenge:`${encodeURIComponent(a)}`,code_challenge_method:`${encodeURIComponent(b)}`});d.push(c.toString())}if(null==c?void 0:c.queryParams){let a=new URLSearchParams(c.queryParams);d.push(a.toString())}return(null==c?void 0:c.skipBrowserRedirect)&&d.push(`skip_http_redirect=${c.skipBrowserRedirect}`),`${a}?${d.join("&")}`}async _unenroll(a){try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;return e?{data:null,error:e}:await dd(this.fetch,"DELETE",`${this.url}/factors/${a.factorId}`,{headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token})})}catch(a){if(cA(a))return{data:null,error:a};throw a}}async _enroll(a){try{return await this._useSession(async b=>{var c,d;let{data:e,error:f}=b;if(f)return{data:null,error:f};let g=Object.assign({friendly_name:a.friendlyName,factor_type:a.factorType},"phone"===a.factorType?{phone:a.phone}:{issuer:a.issuer}),{data:h,error:i}=await dd(this.fetch,"POST",`${this.url}/factors`,{body:g,headers:this.headers,jwt:null==(c=null==e?void 0:e.session)?void 0:c.access_token});return i?{data:null,error:i}:("totp"===a.factorType&&(null==(d=null==h?void 0:h.totp)?void 0:d.qr_code)&&(h.totp.qr_code=`data:image/svg+xml;utf-8,${h.totp.qr_code}`),{data:h,error:null})})}catch(a){if(cA(a))return{data:null,error:a};throw a}}async _verify(a){return this._acquireLock(-1,async()=>{try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;if(e)return{data:null,error:e};let{data:f,error:g}=await dd(this.fetch,"POST",`${this.url}/factors/${a.factorId}/verify`,{body:{code:a.code,challenge_id:a.challengeId},headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token});return g?{data:null,error:g}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:g})})}catch(a){if(cA(a))return{data:null,error:a};throw a}})}async _challenge(a){return this._acquireLock(-1,async()=>{try{return await this._useSession(async b=>{var c;let{data:d,error:e}=b;return e?{data:null,error:e}:await dd(this.fetch,"POST",`${this.url}/factors/${a.factorId}/challenge`,{body:{channel:a.channel},headers:this.headers,jwt:null==(c=null==d?void 0:d.session)?void 0:c.access_token})})}catch(a){if(cA(a))return{data:null,error:a};throw a}})}async _challengeAndVerify(a){let{data:b,error:c}=await this._challenge({factorId:a.factorId});return c?{data:null,error:c}:await this._verify({factorId:a.factorId,challengeId:b.id,code:a.code})}async _listFactors(){let{data:{user:a},error:b}=await this.getUser();if(b)return{data:null,error:b};let c=(null==a?void 0:a.factors)||[],d=c.filter(a=>"totp"===a.factor_type&&"verified"===a.status),e=c.filter(a=>"phone"===a.factor_type&&"verified"===a.status);return{data:{all:c,totp:d,phone:e},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async a=>{var b,c;let{data:{session:d},error:e}=a;if(e)return{data:null,error:e};if(!d)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:f}=c_(d.access_token),g=null;f.aal&&(g=f.aal);let h=g;return(null!=(c=null==(b=d.user.factors)?void 0:b.filter(a=>"verified"===a.status))?c:[]).length>0&&(h="aal2"),{data:{currentLevel:g,nextLevel:h,currentAuthenticationMethods:f.amr||[]},error:null}}))}async fetchJwk(a,b={keys:[]}){let c=b.keys.find(b=>b.kid===a);if(c)return c;let d=Date.now();if((c=this.jwks.keys.find(b=>b.kid===a))&&this.jwks_cached_at+6e5>d)return c;let{data:e,error:f}=await dd(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(f)throw f;return e.keys&&0!==e.keys.length&&(this.jwks=e,this.jwks_cached_at=d,c=e.keys.find(b=>b.kid===a))?c:null}async getClaims(a,b={}){try{let c=a;if(!c){let{data:a,error:b}=await this.getSession();if(b||!a.session)return{data:null,error:b};c=a.session.access_token}let{header:d,payload:e,signature:f,raw:{header:g,payload:h}}=c_(c);(null==b?void 0:b.allowExpired)||function(a){if(!a)throw Error("Missing exp claim");if(a<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(e.exp);let i=!d.alg||d.alg.startsWith("HS")||!d.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:await this.fetchJwk(d.kid,(null==b?void 0:b.keys)?{keys:b.keys}:null==b?void 0:b.jwks);if(!i){let{error:a}=await this.getUser(c);if(a)throw a;return{data:{claims:e,header:d,signature:f},error:null}}let j=function(a){switch(a){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(d.alg),k=await crypto.subtle.importKey("jwk",i,j,!0,["verify"]);if(!await crypto.subtle.verify(j,k,f,function(a){let b=[];return!function(a,b){for(let c=0;c<a.length;c+=1){let d=a.charCodeAt(c);if(d>55295&&d<=56319){let b=(d-55296)*1024&65535;d=(a.charCodeAt(c+1)-56320&65535|b)+65536,c+=1}!function(a,b){if(a<=127)return b(a);if(a<=2047){b(192|a>>6),b(128|63&a);return}if(a<=65535){b(224|a>>12),b(128|a>>6&63),b(128|63&a);return}if(a<=1114111){b(240|a>>18),b(128|a>>12&63),b(128|a>>6&63),b(128|63&a);return}throw Error(`Unrecognized Unicode codepoint: ${a.toString(16)}`)}(d,b)}}(a,a=>b.push(a)),new Uint8Array(b)}(`${g}.${h}`)))throw new cM("Invalid JWT signature");return{data:{claims:e,header:d,signature:f},error:null}}catch(a){if(cA(a))return{data:null,error:a};throw a}}}dx.nextInstanceID=0;let dy=dx;class dz extends dy{constructor(a){super(a)}}class dA{constructor(a,b,c){var d,e,f;if(this.supabaseUrl=a,this.supabaseKey=b,!a)throw Error("supabaseUrl is required.");if(!b)throw Error("supabaseKey is required.");let g=new URL(function(a){return a.endsWith("/")?a:a+"/"}(a));this.realtimeUrl=new URL("realtime/v1",g),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",g),this.storageUrl=new URL("storage/v1",g),this.functionsUrl=new URL("functions/v1",g);let h=`sb-${g.hostname.split(".")[0]}-auth-token`,i=function(a,b){var c,d;let{db:e,auth:f,realtime:g,global:h}=a,{db:i,auth:j,realtime:k,global:l}=b,m={db:Object.assign(Object.assign({},i),e),auth:Object.assign(Object.assign({},j),f),realtime:Object.assign(Object.assign({},k),g),global:Object.assign(Object.assign(Object.assign({},l),h),{headers:Object.assign(Object.assign({},null!=(c=null==l?void 0:l.headers)?c:{}),null!=(d=null==h?void 0:h.headers)?d:{})}),accessToken:()=>{var a,b,c,d;return a=this,b=void 0,d=function*(){return""},new(c=void 0,c=Promise)(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})}};return a.accessToken?m.accessToken=a.accessToken:delete m.accessToken,m}(null!=c?c:{},{db:cq,realtime:cs,auth:Object.assign(Object.assign({},cr),{storageKey:h}),global:cp});this.storageKey=null!=(d=i.auth.storageKey)?d:"",this.headers=null!=(e=i.global.headers)?e:{},i.accessToken?(this.accessToken=i.accessToken,this.auth=new Proxy({},{get:(a,b)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(b)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(f=i.auth)?f:{},this.headers,i.global.fetch),this.fetch=((a,b,c)=>{let d=(a=>{let b;return b=a||("undefined"==typeof fetch?ct.default:fetch),(...a)=>b(...a)})(c),e="undefined"==typeof Headers?ct.Headers:Headers;return(c,f)=>(function(a,b,c,d){return new(c||(c=Promise))(function(e,f){function g(a){try{i(d.next(a))}catch(a){f(a)}}function h(a){try{i(d.throw(a))}catch(a){f(a)}}function i(a){var b;a.done?e(a.value):((b=a.value)instanceof c?b:new c(function(a){a(b)})).then(g,h)}i((d=d.apply(a,b||[])).next())})})(void 0,void 0,void 0,function*(){var g;let h=null!=(g=yield b())?g:a,i=new e(null==f?void 0:f.headers);return i.has("apikey")||i.set("apikey",a),i.has("Authorization")||i.set("Authorization",`Bearer ${h}`),d(c,Object.assign(Object.assign({},f),{headers:i}))})})(b,this._getAccessToken.bind(this),i.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},i.realtime)),this.rest=new bG(new URL("rest/v1",g).href,{headers:this.headers,schema:i.db.schema,fetch:this.fetch}),i.accessToken||this._listenForAuthEvents()}get functions(){return new bF(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new cn(this.storageUrl.href,this.headers,this.fetch)}from(a){return this.rest.from(a)}schema(a){return this.rest.schema(a)}rpc(a,b={},c={}){return this.rest.rpc(a,b,c)}channel(a,b={config:{}}){return this.realtime.channel(a,b)}getChannels(){return this.realtime.getChannels()}removeChannel(a){return this.realtime.removeChannel(a)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var a,b,c,d,e,f;return c=this,d=void 0,e=void 0,f=function*(){if(this.accessToken)return yield this.accessToken();let{data:c}=yield this.auth.getSession();return null!=(b=null==(a=c.session)?void 0:a.access_token)?b:null},new(e||(e=Promise))(function(a,b){function g(a){try{i(f.next(a))}catch(a){b(a)}}function h(a){try{i(f.throw(a))}catch(a){b(a)}}function i(b){var c;b.done?a(b.value):((c=b.value)instanceof e?c:new e(function(a){a(c)})).then(g,h)}i((f=f.apply(c,d||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:a,persistSession:b,detectSessionInUrl:c,storage:d,storageKey:e,flowType:f,lock:g,debug:h},i,j){let k={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new dz({url:this.authUrl.href,headers:Object.assign(Object.assign({},k),i),storageKey:e,autoRefreshToken:a,persistSession:b,detectSessionInUrl:c,storage:d,flowType:f,lock:g,debug:h,fetch:j,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(a){return new b2(this.realtimeUrl.href,Object.assign(Object.assign({},a),{params:Object.assign({apikey:this.supabaseKey},null==a?void 0:a.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((a,b)=>{this._handleTokenChanged(a,"CLIENT",null==b?void 0:b.access_token)})}_handleTokenChanged(a,b,c){("TOKEN_REFRESHED"===a||"SIGNED_IN"===a)&&this.changedAccessToken!==c?this.changedAccessToken=c:"SIGNED_OUT"===a&&(this.realtime.setAuth(),"STORAGE"==b&&this.auth.signOut(),this.changedAccessToken=void 0)}}c(280),"undefined"==typeof URLPattern||URLPattern;var dB=c(815);if(new WeakMap,dB.unstable_postpone,!1===function(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}("Route %%% needs to bail out of prerendering at this point because it used ^^^. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error"))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});async function dC(a){let b=aa.next({request:a}),c=function(a,b,c){if(!a||!b)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:d,getAll:e,setAll:f,setItems:g,removedItems:h}=function(a,b){let c,d,e=a.cookies??null,f=a.cookieEncoding,g={},h={};if(e)if("get"in e){let a=async a=>{let b=a.flatMap(a=>[a,...Array.from({length:5}).map((b,c)=>`${a}.${c}`)]),c=[];for(let a=0;a<b.length;a+=1){let d=await e.get(b[a]);(d||"string"==typeof d)&&c.push({name:b[a],value:d})}return c};if(c=async b=>await a(b),"set"in e&&"remove"in e)d=async a=>{for(let b=0;b<a.length;b+=1){let{name:c,value:d,options:f}=a[b];d?await e.set(c,d,f):await e.remove(c,f)}};else if(b)d=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in e)if(c=async()=>await e.getAll(),"setAll"in e)d=e.setAll;else if(b)d=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${b?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${bo()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!b&&bo())c=()=>(()=>{let a=(0,bn.qg)(document.cookie);return Object.keys(a).map(b=>({name:b,value:a[b]??""}))})(),d=a=>{a.forEach(({name:a,value:b,options:c})=>{document.cookie=(0,bn.lK)(a,b,c)})};else if(b)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else c=()=>[],d=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return b?{getAll:c,setAll:d,setItems:g,removedItems:h,storage:{isServer:!0,getItem:async a=>{if("string"==typeof g[a])return g[a];if(h[a])return null;let b=await c([a]),d=await bt(a,async a=>{let c=b?.find(({name:b})=>b===a)||null;return c?c.value:null});if(!d)return null;let e=d;return"string"==typeof d&&d.startsWith(bz)&&(e=by(d.substring(bz.length))),e},setItem:async(b,e)=>{b.endsWith("-code-verifier")&&await bA({getAll:c,setAll:d,setItems:{[b]:e},removedItems:{}},{cookieOptions:a?.cookieOptions??null,cookieEncoding:f}),g[b]=e,delete h[b]},removeItem:async a=>{delete g[a],h[a]=!0}}}:{getAll:c,setAll:d,setItems:g,removedItems:h,storage:{isServer:!1,getItem:async a=>{let b=await c([a]),d=await bt(a,async a=>{let c=b?.find(({name:b})=>b===a)||null;return c?c.value:null});if(!d)return null;let e=d;return d.startsWith(bz)&&(e=by(d.substring(bz.length))),e},setItem:async(b,e)=>{let g=await c([b]),h=new Set((g?.map(({name:a})=>a)||[]).filter(a=>br(a,b))),i=e;"base64url"===f&&(i=bz+bx(e));let j=bs(b,i);j.forEach(({name:a})=>{h.delete(a)});let k={...bp,...a?.cookieOptions,maxAge:0},l={...bp,...a?.cookieOptions,maxAge:bp.maxAge};delete k.name,delete l.name;let m=[...[...h].map(a=>({name:a,value:"",options:k})),...j.map(({name:a,value:b})=>({name:a,value:b,options:l}))];m.length>0&&await d(m)},removeItem:async b=>{let e=await c([b]),f=(e?.map(({name:a})=>a)||[]).filter(a=>br(a,b)),g={...bp,...a?.cookieOptions,maxAge:0};delete g.name,f.length>0&&await d(f.map(a=>({name:a,value:"",options:g})))}}}}({...c,cookieEncoding:c?.cookieEncoding??"base64url"},!0),i=new dA(a,b,{...c,global:{...c?.global,headers:{...c?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createServerClient"}},auth:{...c?.cookieOptions?.name?{storageKey:c.cookieOptions.name}:null,...c?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:d}});return i.auth.onAuthStateChange(async a=>{(Object.keys(g).length>0||Object.keys(h).length>0)&&("SIGNED_IN"===a||"TOKEN_REFRESHED"===a||"USER_UPDATED"===a||"PASSWORD_RECOVERY"===a||"SIGNED_OUT"===a||"MFA_CHALLENGE_VERIFIED"===a)&&await bA({getAll:e,setAll:f,setItems:g,removedItems:h},{cookieOptions:c?.cookieOptions??null,cookieEncoding:c?.cookieEncoding??"base64url"})}),i}("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg",{cookies:{getAll:()=>a.cookies.getAll(),setAll(c){c.forEach(({name:b,value:c})=>a.cookies.set(b,c)),b=aa.next({request:a}),c.forEach(({name:a,value:c,options:d})=>b.cookies.set(a,c,d))}}}),{data:d}=await c.auth.getClaims(),e=d?.claims;if("/"!==a.nextUrl.pathname&&!e&&!a.nextUrl.pathname.startsWith("/login")&&!a.nextUrl.pathname.startsWith("/auth")){let b=a.nextUrl.clone();return b.pathname="/auth/login",aa.redirect(b)}return b}async function dD(a){return await dC(a)}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;let dE={matcher:["/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"]};Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401});let dF={...p},dG=dF.middleware||dF.default,dH="/middleware";if("function"!=typeof dG)throw Object.defineProperty(Error(`The Middleware "${dH}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function dI(a){return bm({...a,page:dH,handler:async(...a)=>{try{return await dG(...a)}catch(e){let b=a[0],c=new URL(b.url),d=c.pathname+c.search;throw await t(e,{path:d,method:b.method,headers:Object.fromEntries(b.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),e}}})}},35:(a,b)=>{"use strict";Symbol.for("react.transitional.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign},128:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.DEFAULT_HEADERS=void 0;let d=c(203);b.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${d.version}`}},201:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getTestReqInfo:function(){return g},withRequest:function(){return f}});let d=new(c(521)).AsyncLocalStorage;function e(a,b){let c=b.header(a,"next-test-proxy-port");if(!c)return;let d=b.url(a);return{url:d,proxyPort:Number(c),testData:b.header(a,"next-test-data")||""}}function f(a,b,c){let f=e(a,b);return f?d.run(f,c):c()}function g(a,b){let c=d.getStore();return c||(a&&b?e(a,b):void 0)}},203:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.version=void 0,b.version="0.0.0-automated"},279:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0});let e=d(c(3)),f=d(c(784));class g{constructor(a){this.shouldThrowOnError=!1,this.method=a.method,this.url=a.url,this.headers=a.headers,this.schema=a.schema,this.body=a.body,this.shouldThrowOnError=a.shouldThrowOnError,this.signal=a.signal,this.isMaybeSingle=a.isMaybeSingle,a.fetch?this.fetch=a.fetch:"undefined"==typeof fetch?this.fetch=e.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(a,b){return this.headers=Object.assign({},this.headers),this.headers[a]=b,this}then(a,b){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let c=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async a=>{var b,c,d;let e=null,g=null,h=null,i=a.status,j=a.statusText;if(a.ok){if("HEAD"!==this.method){let b=await a.text();""===b||(g="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?b:JSON.parse(b))}let d=null==(b=this.headers.Prefer)?void 0:b.match(/count=(exact|planned|estimated)/),f=null==(c=a.headers.get("content-range"))?void 0:c.split("/");d&&f&&f.length>1&&(h=parseInt(f[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(g)&&(g.length>1?(e={code:"PGRST116",details:`Results contain ${g.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},g=null,h=null,i=406,j="Not Acceptable"):g=1===g.length?g[0]:null)}else{let b=await a.text();try{e=JSON.parse(b),Array.isArray(e)&&404===a.status&&(g=[],e=null,i=200,j="OK")}catch(c){404===a.status&&""===b?(i=204,j="No Content"):e={message:b}}if(e&&this.isMaybeSingle&&(null==(d=null==e?void 0:e.details)?void 0:d.includes("0 rows"))&&(e=null,i=200,j="OK"),e&&this.shouldThrowOnError)throw new f.default(e)}return{error:e,data:g,count:h,status:i,statusText:j}});return this.shouldThrowOnError||(c=c.catch(a=>{var b,c,d;return{error:{message:`${null!=(b=null==a?void 0:a.name)?b:"FetchError"}: ${null==a?void 0:a.message}`,details:`${null!=(c=null==a?void 0:a.stack)?c:""}`,hint:"",code:`${null!=(d=null==a?void 0:a.code)?d:""}`},data:null,count:null,status:0,statusText:""}})),c.then(a,b)}returns(){return this}overrideTypes(){return this}}b.default=g},280:(a,b,c)=>{var d;(()=>{var e={226:function(e,f){!function(g,h){"use strict";var i="function",j="undefined",k="object",l="string",m="major",n="model",o="name",p="type",q="vendor",r="version",s="architecture",t="console",u="mobile",v="tablet",w="smarttv",x="wearable",y="embedded",z="Amazon",A="Apple",B="ASUS",C="BlackBerry",D="Browser",E="Chrome",F="Firefox",G="Google",H="Huawei",I="Microsoft",J="Motorola",K="Opera",L="Samsung",M="Sharp",N="Sony",O="Xiaomi",P="Zebra",Q="Facebook",R="Chromium OS",S="Mac OS",T=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},U=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},V=function(a,b){return typeof a===l&&-1!==W(b).indexOf(W(a))},W=function(a){return a.toLowerCase()},X=function(a,b){if(typeof a===l)return a=a.replace(/^\s\s*/,""),typeof b===j?a:a.substring(0,350)},Y=function(a,b){for(var c,d,e,f,g,j,l=0;l<b.length&&!g;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!g&&m[c];)if(g=m[c++].exec(a))for(e=0;e<n.length;e++)j=g[++d],typeof(f=n[e])===k&&f.length>0?2===f.length?typeof f[1]==i?this[f[0]]=f[1].call(this,j):this[f[0]]=f[1]:3===f.length?typeof f[1]!==i||f[1].exec&&f[1].test?this[f[0]]=j?j.replace(f[1],f[2]):void 0:this[f[0]]=j?f[1].call(this,j,f[2]):void 0:4===f.length&&(this[f[0]]=j?f[3].call(this,j.replace(f[1],f[2])):h):this[f]=j||h;l+=2}},Z=function(a,b){for(var c in b)if(typeof b[c]===k&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(V(b[c][d],a))return"?"===c?h:c}else if(V(b[c],a))return"?"===c?h:c;return a},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,r],[/opios[\/ ]+([\w\.]+)/i],[r,[o,K+" Mini"]],[/\bopr\/([\w\.]+)/i],[r,[o,K]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[o,r],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[r,[o,"UC"+D]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[r,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[r,[o,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[r,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure "+D],r],[/\bfocus\/([\w\.]+)/i],[r,[o,F+" Focus"]],[/\bopt\/([\w\.]+)/i],[r,[o,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[r,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[r,[o,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[r,[o,"MIUI "+D]],[/fxios\/([-\w\.]+)/i],[r,[o,F]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 "+D]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 "+D],r],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],r],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,r],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,Q],r],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,r],[/\bgsa\/([\w\.]+) .*safari\//i],[r,[o,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[r,[o,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[r,[o,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,E+" WebView"],r],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[r,[o,"Android "+D]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,r],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[r,[o,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[r,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[r,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,r],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],r],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[r,[o,F+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[o,r],[/(cobalt)\/([\w\.]+)/i],[o,[r,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,W]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[s,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[s,"armhf"]],[/windows (ce|mobile); ppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[s,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[s,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[n,[q,L],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[n,[q,L],[p,u]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[n,[q,A],[p,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[n,[q,A],[p,v]],[/(macintosh);/i],[n,[q,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[n,[q,M],[p,u]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[n,[q,H],[p,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[n,[q,H],[p,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[n,[q,"OPPO"],[p,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[n,[q,"Vivo"],[p,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[n,[q,"Realme"],[p,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[n,[q,J],[p,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[n,[q,J],[p,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[n,[q,"LG"],[p,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[n,[q,"LG"],[p,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[n,[q,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[n,/_/g," "],[q,"Nokia"],[p,u]],[/(pixel c)\b/i],[n,[q,G],[p,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[n,[q,G],[p,u]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[n,[q,N],[p,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[n,"Xperia Tablet"],[q,N],[p,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[n,[q,"OnePlus"],[p,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[n,[q,z],[p,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[n,/(.+)/g,"Fire Phone $1"],[q,z],[p,u]],[/(playbook);[-\w\),; ]+(rim)/i],[n,q,[p,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[n,[q,C],[p,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[n,[q,B],[p,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[n,[q,B],[p,u]],[/(nexus 9)/i],[n,[q,"HTC"],[p,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[n,/_/g," "],[p,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[n,[q,"Acer"],[p,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[n,[q,"Meizu"],[p,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,n,[p,u]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[q,n,[p,v]],[/(surface duo)/i],[n,[q,I],[p,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[n,[q,"Fairphone"],[p,u]],[/(u304aa)/i],[n,[q,"AT&T"],[p,u]],[/\bsie-(\w*)/i],[n,[q,"Siemens"],[p,u]],[/\b(rct\w+) b/i],[n,[q,"RCA"],[p,v]],[/\b(venue[\d ]{2,7}) b/i],[n,[q,"Dell"],[p,v]],[/\b(q(?:mv|ta)\w+) b/i],[n,[q,"Verizon"],[p,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[n,[q,"Barnes & Noble"],[p,v]],[/\b(tm\d{3}\w+) b/i],[n,[q,"NuVision"],[p,v]],[/\b(k88) b/i],[n,[q,"ZTE"],[p,v]],[/\b(nx\d{3}j) b/i],[n,[q,"ZTE"],[p,u]],[/\b(gen\d{3}) b.+49h/i],[n,[q,"Swiss"],[p,u]],[/\b(zur\d{3}) b/i],[n,[q,"Swiss"],[p,v]],[/\b((zeki)?tb.*\b) b/i],[n,[q,"Zeki"],[p,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[q,"Dragon Touch"],n,[p,v]],[/\b(ns-?\w{0,9}) b/i],[n,[q,"Insignia"],[p,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[n,[q,"NextBook"],[p,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[q,"Voice"],n,[p,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[q,"LvTel"],n,[p,u]],[/\b(ph-1) /i],[n,[q,"Essential"],[p,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[n,[q,"Envizen"],[p,v]],[/\b(trio[-\w\. ]+) b/i],[n,[q,"MachSpeed"],[p,v]],[/\btu_(1491) b/i],[n,[q,"Rotor"],[p,v]],[/(shield[\w ]+) b/i],[n,[q,"Nvidia"],[p,v]],[/(sprint) (\w+)/i],[q,n,[p,u]],[/(kin\.[onetw]{3})/i],[[n,/\./g," "],[q,I],[p,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[n,[q,P],[p,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[n,[q,P],[p,u]],[/smart-tv.+(samsung)/i],[q,[p,w]],[/hbbtv.+maple;(\d+)/i],[[n,/^/,"SmartTV"],[q,L],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,"LG"],[p,w]],[/(apple) ?tv/i],[q,[n,A+" TV"],[p,w]],[/crkey/i],[[n,E+"cast"],[q,G],[p,w]],[/droid.+aft(\w)( bui|\))/i],[n,[q,z],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[n,[q,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[n,[q,N],[p,w]],[/(mitv-\w{5}) bui/i],[n,[q,O],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[q,n,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,X],[n,X],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[q,n,[p,t]],[/droid.+; (shield) bui/i],[n,[q,"Nvidia"],[p,t]],[/(playstation [345portablevi]+)/i],[n,[q,N],[p,t]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[n,[q,I],[p,t]],[/((pebble))app/i],[q,n,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[n,[q,A],[p,x]],[/droid.+; (glass) \d/i],[n,[q,G],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[n,[q,P],[p,x]],[/(quest( 2| pro)?)/i],[n,[q,Q],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[p,y]],[/(aeobc)\b/i],[n,[q,z],[p,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[n,[p,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[n,[p,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,u]],[/(android[-\w\. ]{0,9});.+buil/i],[n,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[r,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[o,r],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,r],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[r,Z,$]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[r,Z,$]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[r,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,S],[r,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,r],[/\(bb(10);/i],[r,[o,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[r,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[r,[o,F+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r,[o,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[r,[o,"watchOS"]],[/crkey\/([\d\.]+)/i],[r,[o,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[o,R],r],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,r],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],r],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[o,r]]},aa=function(a,b){if(typeof a===k&&(b=a,a=h),!(this instanceof aa))return new aa(a,b).getResult();var c=typeof g!==j&&g.navigator?g.navigator:h,d=a||(c&&c.userAgent?c.userAgent:""),e=c&&c.userAgentData?c.userAgentData:h,f=b?T(_,b):_,t=c&&c.userAgent==d;return this.getBrowser=function(){var a,b={};return b[o]=h,b[r]=h,Y.call(b,d,f.browser),b[m]=typeof(a=b[r])===l?a.replace(/[^\d\.]/g,"").split(".")[0]:h,t&&c&&c.brave&&typeof c.brave.isBrave==i&&(b[o]="Brave"),b},this.getCPU=function(){var a={};return a[s]=h,Y.call(a,d,f.cpu),a},this.getDevice=function(){var a={};return a[q]=h,a[n]=h,a[p]=h,Y.call(a,d,f.device),t&&!a[p]&&e&&e.mobile&&(a[p]=u),t&&"Macintosh"==a[n]&&c&&typeof c.standalone!==j&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[n]="iPad",a[p]=v),a},this.getEngine=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.engine),a},this.getOS=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.os),t&&!a[o]&&e&&"Unknown"!=e.platform&&(a[o]=e.platform.replace(/chrome os/i,R).replace(/macos/i,S)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return d},this.setUA=function(a){return d=typeof a===l&&a.length>350?X(a,350):a,this},this.setUA(d),this};aa.VERSION="1.0.35",aa.BROWSER=U([o,r,m]),aa.CPU=U([s]),aa.DEVICE=U([n,q,p,t,u,w,v,x,y]),aa.ENGINE=aa.OS=U([o,r]),typeof f!==j?(e.exports&&(f=e.exports=aa),f.UAParser=aa):c.amdO?void 0===(d=(function(){return aa}).call(b,c,b,a))||(a.exports=d):typeof g!==j&&(g.UAParser=aa);var ab=typeof g!==j&&(g.jQuery||g.Zepto);if(ab&&!ab.ua){var ac=new aa;ab.ua=ac.getResult(),ab.ua.get=function(){return ac.getUA()},ab.ua.set=function(a){ac.setUA(a);var b=ac.getResult();for(var c in b)ab.ua[c]=b[c]}}}("object"==typeof window?window:this)}},f={};function g(a){var b=f[a];if(void 0!==b)return b.exports;var c=f[a]={exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,g),d=!1}finally{d&&delete f[a]}return c.exports}g.ab="//",a.exports=g(226)})()},355:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0}),b.PostgrestError=b.PostgrestBuilder=b.PostgrestTransformBuilder=b.PostgrestFilterBuilder=b.PostgrestQueryBuilder=b.PostgrestClient=void 0;let e=d(c(729));b.PostgrestClient=e.default;let f=d(c(665));b.PostgrestQueryBuilder=f.default;let g=d(c(373));b.PostgrestFilterBuilder=g.default;let h=d(c(861));b.PostgrestTransformBuilder=h.default;let i=d(c(279));b.PostgrestBuilder=i.default;let j=d(c(784));b.PostgrestError=j.default,b.default={PostgrestClient:e.default,PostgrestQueryBuilder:f.default,PostgrestFilterBuilder:g.default,PostgrestTransformBuilder:h.default,PostgrestBuilder:i.default,PostgrestError:j.default}},356:a=>{"use strict";a.exports=require("node:buffer")},373:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0});let e=d(c(861));class f extends e.default{eq(a,b){return this.url.searchParams.append(a,`eq.${b}`),this}neq(a,b){return this.url.searchParams.append(a,`neq.${b}`),this}gt(a,b){return this.url.searchParams.append(a,`gt.${b}`),this}gte(a,b){return this.url.searchParams.append(a,`gte.${b}`),this}lt(a,b){return this.url.searchParams.append(a,`lt.${b}`),this}lte(a,b){return this.url.searchParams.append(a,`lte.${b}`),this}like(a,b){return this.url.searchParams.append(a,`like.${b}`),this}likeAllOf(a,b){return this.url.searchParams.append(a,`like(all).{${b.join(",")}}`),this}likeAnyOf(a,b){return this.url.searchParams.append(a,`like(any).{${b.join(",")}}`),this}ilike(a,b){return this.url.searchParams.append(a,`ilike.${b}`),this}ilikeAllOf(a,b){return this.url.searchParams.append(a,`ilike(all).{${b.join(",")}}`),this}ilikeAnyOf(a,b){return this.url.searchParams.append(a,`ilike(any).{${b.join(",")}}`),this}is(a,b){return this.url.searchParams.append(a,`is.${b}`),this}in(a,b){let c=Array.from(new Set(b)).map(a=>"string"==typeof a&&RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(a,`in.(${c})`),this}contains(a,b){return"string"==typeof b?this.url.searchParams.append(a,`cs.${b}`):Array.isArray(b)?this.url.searchParams.append(a,`cs.{${b.join(",")}}`):this.url.searchParams.append(a,`cs.${JSON.stringify(b)}`),this}containedBy(a,b){return"string"==typeof b?this.url.searchParams.append(a,`cd.${b}`):Array.isArray(b)?this.url.searchParams.append(a,`cd.{${b.join(",")}}`):this.url.searchParams.append(a,`cd.${JSON.stringify(b)}`),this}rangeGt(a,b){return this.url.searchParams.append(a,`sr.${b}`),this}rangeGte(a,b){return this.url.searchParams.append(a,`nxl.${b}`),this}rangeLt(a,b){return this.url.searchParams.append(a,`sl.${b}`),this}rangeLte(a,b){return this.url.searchParams.append(a,`nxr.${b}`),this}rangeAdjacent(a,b){return this.url.searchParams.append(a,`adj.${b}`),this}overlaps(a,b){return"string"==typeof b?this.url.searchParams.append(a,`ov.${b}`):this.url.searchParams.append(a,`ov.{${b.join(",")}}`),this}textSearch(a,b,{config:c,type:d}={}){let e="";"plain"===d?e="pl":"phrase"===d?e="ph":"websearch"===d&&(e="w");let f=void 0===c?"":`(${c})`;return this.url.searchParams.append(a,`${e}fts${f}.${b}`),this}match(a){return Object.entries(a).forEach(([a,b])=>{this.url.searchParams.append(a,`eq.${b}`)}),this}not(a,b,c){return this.url.searchParams.append(a,`not.${b}.${c}`),this}or(a,{foreignTable:b,referencedTable:c=b}={}){let d=c?`${c}.or`:"or";return this.url.searchParams.append(d,`(${a})`),this}filter(a,b,c){return this.url.searchParams.append(a,`${b}.${c}`),this}}b.default=f},521:a=>{"use strict";a.exports=require("node:async_hooks")},552:(a,b,c)=>{"use strict";var d=c(356).Buffer;Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleFetch:function(){return h},interceptFetch:function(){return i},reader:function(){return f}});let e=c(201),f={url:a=>a.url,header:(a,b)=>a.headers.get(b)};async function g(a,b){let{url:c,method:e,headers:f,body:g,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}=b;return{testData:a,api:"fetch",request:{url:c,method:e,headers:[...Array.from(f),["next-test-stack",function(){let a=(Error().stack??"").split("\n");for(let b=1;b<a.length;b++)if(a[b].length>0){a=a.slice(b);break}return(a=(a=(a=a.filter(a=>!a.includes("/next/dist/"))).slice(0,5)).map(a=>a.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:g?d.from(await b.arrayBuffer()).toString("base64"):null,cache:h,credentials:i,integrity:j,mode:k,redirect:l,referrer:m,referrerPolicy:n}}}async function h(a,b){let c=(0,e.getTestReqInfo)(b,f);if(!c)return a(b);let{testData:h,proxyPort:i}=c,j=await g(h,b),k=await a(`http://localhost:${i}`,{method:"POST",body:JSON.stringify(j),next:{internal:!0}});if(!k.ok)throw Object.defineProperty(Error(`Proxy request failed: ${k.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let l=await k.json(),{api:m}=l;switch(m){case"continue":return a(b);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${b.method} ${b.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:n,headers:o,body:p}=l.response;return new Response(p?d.from(p,"base64"):null,{status:n,headers:new Headers(o)})}function i(a){return c.g.fetch=function(b,c){var d;return(null==c||null==(d=c.next)?void 0:d.internal)?a(b,c):h(a,new Request(b,c))},()=>{c.g.fetch=a}}},554:(a,b)=>{"use strict";b.qg=function(a,b){let c=new h,d=a.length;if(d<2)return c;let e=b?.decode||k,f=0;do{let b=a.indexOf("=",f);if(-1===b)break;let g=a.indexOf(";",f),h=-1===g?d:g;if(b>h){f=a.lastIndexOf(";",b-1)+1;continue}let k=i(a,f,b),l=j(a,b,k),m=a.slice(k,l);if(void 0===c[m]){let d=i(a,b+1,h),f=j(a,h,d),g=e(a.slice(d,f));c[m]=g}f=h+1}while(f<d);return c},b.lK=function(a,b,h){let i=h?.encode||encodeURIComponent;if(!c.test(a))throw TypeError(`argument name is invalid: ${a}`);let j=i(b);if(!d.test(j))throw TypeError(`argument val is invalid: ${b}`);let k=a+"="+j;if(!h)return k;if(void 0!==h.maxAge){if(!Number.isInteger(h.maxAge))throw TypeError(`option maxAge is invalid: ${h.maxAge}`);k+="; Max-Age="+h.maxAge}if(h.domain){if(!e.test(h.domain))throw TypeError(`option domain is invalid: ${h.domain}`);k+="; Domain="+h.domain}if(h.path){if(!f.test(h.path))throw TypeError(`option path is invalid: ${h.path}`);k+="; Path="+h.path}if(h.expires){var l;if(l=h.expires,"[object Date]"!==g.call(l)||!Number.isFinite(h.expires.valueOf()))throw TypeError(`option expires is invalid: ${h.expires}`);k+="; Expires="+h.expires.toUTCString()}if(h.httpOnly&&(k+="; HttpOnly"),h.secure&&(k+="; Secure"),h.partitioned&&(k+="; Partitioned"),h.priority)switch("string"==typeof h.priority?h.priority.toLowerCase():void 0){case"low":k+="; Priority=Low";break;case"medium":k+="; Priority=Medium";break;case"high":k+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${h.priority}`)}if(h.sameSite)switch("string"==typeof h.sameSite?h.sameSite.toLowerCase():h.sameSite){case!0:case"strict":k+="; SameSite=Strict";break;case"lax":k+="; SameSite=Lax";break;case"none":k+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${h.sameSite}`)}return k};let c=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,d=/^[\u0021-\u003A\u003C-\u007E]*$/,e=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,f=/^[\u0020-\u003A\u003D-\u007E]*$/,g=Object.prototype.toString,h=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function i(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c);return c}function j(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function k(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}},665:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0});let e=d(c(373));class f{constructor(a,{headers:b={},schema:c,fetch:d}){this.url=a,this.headers=b,this.schema=c,this.fetch=d}select(a,{head:b=!1,count:c}={}){let d=!1,f=(null!=a?a:"*").split("").map(a=>/\s/.test(a)&&!d?"":('"'===a&&(d=!d),a)).join("");return this.url.searchParams.set("select",f),c&&(this.headers.Prefer=`count=${c}`),new e.default({method:b?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(a,{count:b,defaultToNull:c=!0}={}){let d=[];if(this.headers.Prefer&&d.push(this.headers.Prefer),b&&d.push(`count=${b}`),c||d.push("missing=default"),this.headers.Prefer=d.join(","),Array.isArray(a)){let b=a.reduce((a,b)=>a.concat(Object.keys(b)),[]);if(b.length>0){let a=[...new Set(b)].map(a=>`"${a}"`);this.url.searchParams.set("columns",a.join(","))}}return new e.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}upsert(a,{onConflict:b,ignoreDuplicates:c=!1,count:d,defaultToNull:f=!0}={}){let g=[`resolution=${c?"ignore":"merge"}-duplicates`];if(void 0!==b&&this.url.searchParams.set("on_conflict",b),this.headers.Prefer&&g.push(this.headers.Prefer),d&&g.push(`count=${d}`),f||g.push("missing=default"),this.headers.Prefer=g.join(","),Array.isArray(a)){let b=a.reduce((a,b)=>a.concat(Object.keys(b)),[]);if(b.length>0){let a=[...new Set(b)].map(a=>`"${a}"`);this.url.searchParams.set("columns",a.join(","))}}return new e.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(a,{count:b}={}){let c=[];return this.headers.Prefer&&c.push(this.headers.Prefer),b&&c.push(`count=${b}`),this.headers.Prefer=c.join(","),new e.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}delete({count:a}={}){let b=[];return a&&b.push(`count=${a}`),this.headers.Prefer&&b.unshift(this.headers.Prefer),this.headers.Prefer=b.join(","),new e.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}b.default=f},724:a=>{"use strict";var b=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,e=Object.prototype.hasOwnProperty,f={};function g(a){var b;let c=["path"in a&&a.path&&`Path=${a.path}`,"expires"in a&&(a.expires||0===a.expires)&&`Expires=${("number"==typeof a.expires?new Date(a.expires):a.expires).toUTCString()}`,"maxAge"in a&&"number"==typeof a.maxAge&&`Max-Age=${a.maxAge}`,"domain"in a&&a.domain&&`Domain=${a.domain}`,"secure"in a&&a.secure&&"Secure","httpOnly"in a&&a.httpOnly&&"HttpOnly","sameSite"in a&&a.sameSite&&`SameSite=${a.sameSite}`,"partitioned"in a&&a.partitioned&&"Partitioned","priority"in a&&a.priority&&`Priority=${a.priority}`].filter(Boolean),d=`${a.name}=${encodeURIComponent(null!=(b=a.value)?b:"")}`;return 0===c.length?d:`${d}; ${c.join("; ")}`}function h(a){let b=new Map;for(let c of a.split(/; */)){if(!c)continue;let a=c.indexOf("=");if(-1===a){b.set(c,"true");continue}let[d,e]=[c.slice(0,a),c.slice(a+1)];try{b.set(d,decodeURIComponent(null!=e?e:"true"))}catch{}}return b}function i(a){if(!a)return;let[[b,c],...d]=h(a),{domain:e,expires:f,httponly:g,maxage:i,path:l,samesite:m,secure:n,partitioned:o,priority:p}=Object.fromEntries(d.map(([a,b])=>[a.toLowerCase().replace(/-/g,""),b]));{var q,r,s={name:b,value:decodeURIComponent(c),domain:e,...f&&{expires:new Date(f)},...g&&{httpOnly:!0},..."string"==typeof i&&{maxAge:Number(i)},path:l,...m&&{sameSite:j.includes(q=(q=m).toLowerCase())?q:void 0},...n&&{secure:!0},...p&&{priority:k.includes(r=(r=p).toLowerCase())?r:void 0},...o&&{partitioned:!0}};let a={};for(let b in s)s[b]&&(a[b]=s[b]);return a}}((a,c)=>{for(var d in c)b(a,d,{get:c[d],enumerable:!0})})(f,{RequestCookies:()=>l,ResponseCookies:()=>m,parseCookie:()=>h,parseSetCookie:()=>i,stringifyCookie:()=>g}),a.exports=((a,f,g,h)=>{if(f&&"object"==typeof f||"function"==typeof f)for(let i of d(f))e.call(a,i)||i===g||b(a,i,{get:()=>f[i],enumerable:!(h=c(f,i))||h.enumerable});return a})(b({},"__esModule",{value:!0}),f);var j=["strict","lax","none"],k=["low","medium","high"],l=class{constructor(a){this._parsed=new Map,this._headers=a;let b=a.get("cookie");if(b)for(let[a,c]of h(b))this._parsed.set(a,{name:a,value:c})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed);if(!a.length)return c.map(([a,b])=>b);let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(([a])=>a===d).map(([a,b])=>b)}has(a){return this._parsed.has(a)}set(...a){let[b,c]=1===a.length?[a[0].name,a[0].value]:a,d=this._parsed;return d.set(b,{name:b,value:c}),this._headers.set("cookie",Array.from(d).map(([a,b])=>g(b)).join("; ")),this}delete(a){let b=this._parsed,c=Array.isArray(a)?a.map(a=>b.delete(a)):b.delete(a);return this._headers.set("cookie",Array.from(b).map(([a,b])=>g(b)).join("; ")),c}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a=>`${a.name}=${encodeURIComponent(a.value)}`).join("; ")}},m=class{constructor(a){var b,c,d;this._parsed=new Map,this._headers=a;let e=null!=(d=null!=(c=null==(b=a.getSetCookie)?void 0:b.call(a))?c:a.get("set-cookie"))?d:[];for(let a of Array.isArray(e)?e:function(a){if(!a)return[];var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}(e)){let b=i(a);b&&this._parsed.set(b.name,b)}}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed.values());if(!a.length)return c;let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(a=>a.name===d)}has(a){return this._parsed.has(a)}set(...a){let[b,c,d]=1===a.length?[a[0].name,a[0].value,a[0]]:a,e=this._parsed;return e.set(b,function(a={name:"",value:""}){return"number"==typeof a.expires&&(a.expires=new Date(a.expires)),a.maxAge&&(a.expires=new Date(Date.now()+1e3*a.maxAge)),(null===a.path||void 0===a.path)&&(a.path="/"),a}({name:b,value:c,...d})),function(a,b){for(let[,c]of(b.delete("set-cookie"),a)){let a=g(c);b.append("set-cookie",a)}}(e,this._headers),this}delete(...a){let[b,c]="string"==typeof a[0]?[a[0]]:[a[0].name,a[0]];return this.set({...c,name:b,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(g).join("; ")}}},729:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0});let e=d(c(665)),f=d(c(373)),g=c(128);class h{constructor(a,{headers:b={},schema:c,fetch:d}={}){this.url=a,this.headers=Object.assign(Object.assign({},g.DEFAULT_HEADERS),b),this.schemaName=c,this.fetch=d}from(a){let b=new URL(`${this.url}/${a}`);return new e.default(b,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(a){return new h(this.url,{headers:this.headers,schema:a,fetch:this.fetch})}rpc(a,b={},{head:c=!1,get:d=!1,count:e}={}){let g,h,i=new URL(`${this.url}/rpc/${a}`);c||d?(g=c?"HEAD":"GET",Object.entries(b).filter(([a,b])=>void 0!==b).map(([a,b])=>[a,Array.isArray(b)?`{${b.join(",")}}`:`${b}`]).forEach(([a,b])=>{i.searchParams.append(a,b)})):(g="POST",h=b);let j=Object.assign({},this.headers);return e&&(j.Prefer=`count=${e}`),new f.default({method:g,url:i,headers:j,schema:this.schemaName,body:h,fetch:this.fetch,allowEmpty:!1})}}b.default=h},784:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0});class c extends Error{constructor(a){super(a.message),this.name="PostgrestError",this.details=a.details,this.hint=a.hint,this.code=a.code}}b.default=c},802:a=>{(()=>{"use strict";var b={993:a=>{var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),(new d).__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},213:a=>{a.exports=(a,b)=>(b=b||(()=>{}),a.then(a=>new Promise(a=>{a(b())}).then(()=>a),a=>new Promise(a=>{a(b())}).then(()=>{throw a})))},574:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(a,b,c){let d=0,e=a.length;for(;e>0;){let f=e/2|0,g=d+f;0>=c(a[g],b)?(d=++g,e-=f+1):e=f}return d}},821:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0});let d=c(574);class e{constructor(){this._queue=[]}enqueue(a,b){let c={priority:(b=Object.assign({priority:0},b)).priority,run:a};if(this.size&&this._queue[this.size-1].priority>=b.priority)return void this._queue.push(c);let e=d.default(this._queue,c,(a,b)=>b.priority-a.priority);this._queue.splice(e,0,c)}dequeue(){let a=this._queue.shift();return null==a?void 0:a.run}filter(a){return this._queue.filter(b=>b.priority===a.priority).map(a=>a.run)}get size(){return this._queue.length}}b.default=e},816:(a,b,c)=>{let d=c(213);class e extends Error{constructor(a){super(a),this.name="TimeoutError"}}let f=(a,b,c)=>new Promise((f,g)=>{if("number"!=typeof b||b<0)throw TypeError("Expected `milliseconds` to be a positive number");if(b===1/0)return void f(a);let h=setTimeout(()=>{if("function"==typeof c){try{f(c())}catch(a){g(a)}return}let d="string"==typeof c?c:`Promise timed out after ${b} milliseconds`,h=c instanceof Error?c:new e(d);"function"==typeof a.cancel&&a.cancel(),g(h)},b);d(a.then(f,g),()=>{clearTimeout(h)})});a.exports=f,a.exports.default=f,a.exports.TimeoutError=e}},c={};function d(a){var e=c[a];if(void 0!==e)return e.exports;var f=c[a]={exports:{}},g=!0;try{b[a](f,f.exports,d),g=!1}finally{g&&delete c[a]}return f.exports}d.ab="//";var e={};(()=>{Object.defineProperty(e,"__esModule",{value:!0});let a=d(993),b=d(816),c=d(821),f=()=>{},g=new b.TimeoutError;class h extends a{constructor(a){var b,d,e,g;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=f,this._resolveIdle=f,!("number"==typeof(a=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:c.default},a)).intervalCap&&a.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(d=null==(b=a.intervalCap)?void 0:b.toString())?d:""}\` (${typeof a.intervalCap})`);if(void 0===a.interval||!(Number.isFinite(a.interval)&&a.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(g=null==(e=a.interval)?void 0:e.toString())?g:""}\` (${typeof a.interval})`);this._carryoverConcurrencyCount=a.carryoverConcurrencyCount,this._isIntervalIgnored=a.intervalCap===1/0||0===a.interval,this._intervalCap=a.intervalCap,this._interval=a.interval,this._queue=new a.queueClass,this._queueClass=a.queueClass,this.concurrency=a.concurrency,this._timeout=a.timeout,this._throwOnTimeout=!0===a.throwOnTimeout,this._isPaused=!1===a.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=f,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=f,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let a=Date.now();if(void 0===this._intervalId){let b=this._intervalEnd-a;if(!(b<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},b)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let a=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let b=this._queue.dequeue();return!!b&&(this.emit("active"),b(),a&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(a){if(!("number"==typeof a&&a>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${a}\` (${typeof a})`);this._concurrency=a,this._processQueue()}async add(a,c={}){return new Promise((d,e)=>{let f=async()=>{this._pendingCount++,this._intervalCount++;try{let f=void 0===this._timeout&&void 0===c.timeout?a():b.default(Promise.resolve(a()),void 0===c.timeout?this._timeout:c.timeout,()=>{(void 0===c.throwOnTimeout?this._throwOnTimeout:c.throwOnTimeout)&&e(g)});d(await f)}catch(a){e(a)}this._next()};this._queue.enqueue(f,c),this._tryToStartAnother(),this.emit("add")})}async addAll(a,b){return Promise.all(a.map(async a=>this.add(a,b)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(a=>{let b=this._resolveEmpty;this._resolveEmpty=()=>{b(),a()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(a=>{let b=this._resolveIdle;this._resolveIdle=()=>{b(),a()}})}get size(){return this._queue.size}sizeBy(a){return this._queue.filter(a).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(a){this._timeout=a}}e.default=h})(),a.exports=e})()},815:(a,b,c)=>{"use strict";a.exports=c(35)},861:function(a,b,c){"use strict";var d=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(b,"__esModule",{value:!0});let e=d(c(279));class f extends e.default{select(a){let b=!1,c=(null!=a?a:"*").split("").map(a=>/\s/.test(a)&&!b?"":('"'===a&&(b=!b),a)).join("");return this.url.searchParams.set("select",c),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(a,{ascending:b=!0,nullsFirst:c,foreignTable:d,referencedTable:e=d}={}){let f=e?`${e}.order`:"order",g=this.url.searchParams.get(f);return this.url.searchParams.set(f,`${g?`${g},`:""}${a}.${b?"asc":"desc"}${void 0===c?"":c?".nullsfirst":".nullslast"}`),this}limit(a,{foreignTable:b,referencedTable:c=b}={}){let d=void 0===c?"limit":`${c}.limit`;return this.url.searchParams.set(d,`${a}`),this}range(a,b,{foreignTable:c,referencedTable:d=c}={}){let e=void 0===d?"offset":`${d}.offset`,f=void 0===d?"limit":`${d}.limit`;return this.url.searchParams.set(e,`${a}`),this.url.searchParams.set(f,`${b-a+1}`),this}abortSignal(a){return this.signal=a,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:a=!1,verbose:b=!1,settings:c=!1,buffers:d=!1,wal:e=!1,format:f="text"}={}){var g;let h=[a?"analyze":null,b?"verbose":null,c?"settings":null,d?"buffers":null,e?"wal":null].filter(Boolean).join("|"),i=null!=(g=this.headers.Accept)?g:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${f}; for="${i}"; options=${h};`,this}rollback(){var a;return(null!=(a=this.headers.Prefer)?a:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}b.default=f},890:a=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var b={};(()=>{b.parse=function(b,c){if("string"!=typeof b)throw TypeError("argument str must be a string");for(var e={},f=b.split(d),g=(c||{}).decode||a,h=0;h<f.length;h++){var i=f[h],j=i.indexOf("=");if(!(j<0)){var k=i.substr(0,j).trim(),l=i.substr(++j,i.length).trim();'"'==l[0]&&(l=l.slice(1,-1)),void 0==e[k]&&(e[k]=function(a,b){try{return b(a)}catch(b){return a}}(l,g))}}return e},b.serialize=function(a,b,d){var f=d||{},g=f.encode||c;if("function"!=typeof g)throw TypeError("option encode is invalid");if(!e.test(a))throw TypeError("argument name is invalid");var h=g(b);if(h&&!e.test(h))throw TypeError("argument val is invalid");var i=a+"="+h;if(null!=f.maxAge){var j=f.maxAge-0;if(isNaN(j)||!isFinite(j))throw TypeError("option maxAge is invalid");i+="; Max-Age="+Math.floor(j)}if(f.domain){if(!e.test(f.domain))throw TypeError("option domain is invalid");i+="; Domain="+f.domain}if(f.path){if(!e.test(f.path))throw TypeError("option path is invalid");i+="; Path="+f.path}if(f.expires){if("function"!=typeof f.expires.toUTCString)throw TypeError("option expires is invalid");i+="; Expires="+f.expires.toUTCString()}if(f.httpOnly&&(i+="; HttpOnly"),f.secure&&(i+="; Secure"),f.sameSite)switch("string"==typeof f.sameSite?f.sameSite.toLowerCase():f.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return i};var a=decodeURIComponent,c=encodeURIComponent,d=/; */,e=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),a.exports=b})()},905:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{interceptTestApis:function(){return f},wrapRequestHandler:function(){return g}});let d=c(201),e=c(552);function f(){return(0,e.interceptFetch)(c.g.fetch)}function g(a){return(b,c)=>(0,d.withRequest)(b,e.reader,()=>a(b,c))}},956:(a,b,c)=>{(()=>{"use strict";var b={491:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ContextAPI=void 0;let d=c(223),e=c(172),f=c(930),g="context",h=new d.NoopContextManager;class i{constructor(){}static getInstance(){return this._instance||(this._instance=new i),this._instance}setGlobalContextManager(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}active(){return this._getContextManager().active()}with(a,b,c,...d){return this._getContextManager().with(a,b,c,...d)}bind(a,b){return this._getContextManager().bind(a,b)}_getContextManager(){return(0,e.getGlobal)(g)||h}disable(){this._getContextManager().disable(),(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.ContextAPI=i},930:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagAPI=void 0;let d=c(56),e=c(912),f=c(957),g=c(172);class h{constructor(){function a(a){return function(...b){let c=(0,g.getGlobal)("diag");if(c)return c[a](...b)}}let b=this;b.setLogger=(a,c={logLevel:f.DiagLogLevel.INFO})=>{var d,h,i;if(a===b){let a=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return b.error(null!=(d=a.stack)?d:a.message),!1}"number"==typeof c&&(c={logLevel:c});let j=(0,g.getGlobal)("diag"),k=(0,e.createLogLevelDiagLogger)(null!=(h=c.logLevel)?h:f.DiagLogLevel.INFO,a);if(j&&!c.suppressOverrideMessage){let a=null!=(i=Error().stack)?i:"<failed to generate stacktrace>";j.warn(`Current logger will be overwritten from ${a}`),k.warn(`Current logger will overwrite one already registered from ${a}`)}return(0,g.registerGlobal)("diag",k,b,!0)},b.disable=()=>{(0,g.unregisterGlobal)("diag",b)},b.createComponentLogger=a=>new d.DiagComponentLogger(a),b.verbose=a("verbose"),b.debug=a("debug"),b.info=a("info"),b.warn=a("warn"),b.error=a("error")}static instance(){return this._instance||(this._instance=new h),this._instance}}b.DiagAPI=h},653:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.MetricsAPI=void 0;let d=c(660),e=c(172),f=c(930),g="metrics";class h{constructor(){}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalMeterProvider(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}getMeterProvider(){return(0,e.getGlobal)(g)||d.NOOP_METER_PROVIDER}getMeter(a,b,c){return this.getMeterProvider().getMeter(a,b,c)}disable(){(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.MetricsAPI=h},181:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.PropagationAPI=void 0;let d=c(172),e=c(874),f=c(194),g=c(277),h=c(369),i=c(930),j="propagation",k=new e.NoopTextMapPropagator;class l{constructor(){this.createBaggage=h.createBaggage,this.getBaggage=g.getBaggage,this.getActiveBaggage=g.getActiveBaggage,this.setBaggage=g.setBaggage,this.deleteBaggage=g.deleteBaggage}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalPropagator(a){return(0,d.registerGlobal)(j,a,i.DiagAPI.instance())}inject(a,b,c=f.defaultTextMapSetter){return this._getGlobalPropagator().inject(a,b,c)}extract(a,b,c=f.defaultTextMapGetter){return this._getGlobalPropagator().extract(a,b,c)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,d.unregisterGlobal)(j,i.DiagAPI.instance())}_getGlobalPropagator(){return(0,d.getGlobal)(j)||k}}b.PropagationAPI=l},997:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceAPI=void 0;let d=c(172),e=c(846),f=c(139),g=c(607),h=c(930),i="trace";class j{constructor(){this._proxyTracerProvider=new e.ProxyTracerProvider,this.wrapSpanContext=f.wrapSpanContext,this.isSpanContextValid=f.isSpanContextValid,this.deleteSpan=g.deleteSpan,this.getSpan=g.getSpan,this.getActiveSpan=g.getActiveSpan,this.getSpanContext=g.getSpanContext,this.setSpan=g.setSpan,this.setSpanContext=g.setSpanContext}static getInstance(){return this._instance||(this._instance=new j),this._instance}setGlobalTracerProvider(a){let b=(0,d.registerGlobal)(i,this._proxyTracerProvider,h.DiagAPI.instance());return b&&this._proxyTracerProvider.setDelegate(a),b}getTracerProvider(){return(0,d.getGlobal)(i)||this._proxyTracerProvider}getTracer(a,b){return this.getTracerProvider().getTracer(a,b)}disable(){(0,d.unregisterGlobal)(i,h.DiagAPI.instance()),this._proxyTracerProvider=new e.ProxyTracerProvider}}b.TraceAPI=j},277:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.deleteBaggage=b.setBaggage=b.getActiveBaggage=b.getBaggage=void 0;let d=c(491),e=(0,c(780).createContextKey)("OpenTelemetry Baggage Key");function f(a){return a.getValue(e)||void 0}b.getBaggage=f,b.getActiveBaggage=function(){return f(d.ContextAPI.getInstance().active())},b.setBaggage=function(a,b){return a.setValue(e,b)},b.deleteBaggage=function(a){return a.deleteValue(e)}},993:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.BaggageImpl=void 0;class c{constructor(a){this._entries=a?new Map(a):new Map}getEntry(a){let b=this._entries.get(a);if(b)return Object.assign({},b)}getAllEntries(){return Array.from(this._entries.entries()).map(([a,b])=>[a,b])}setEntry(a,b){let d=new c(this._entries);return d._entries.set(a,b),d}removeEntry(a){let b=new c(this._entries);return b._entries.delete(a),b}removeEntries(...a){let b=new c(this._entries);for(let c of a)b._entries.delete(c);return b}clear(){return new c}}b.BaggageImpl=c},830:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataSymbol=void 0,b.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataFromString=b.createBaggage=void 0;let d=c(930),e=c(993),f=c(830),g=d.DiagAPI.instance();b.createBaggage=function(a={}){return new e.BaggageImpl(new Map(Object.entries(a)))},b.baggageEntryMetadataFromString=function(a){return"string"!=typeof a&&(g.error(`Cannot create baggage metadata from unknown type: ${typeof a}`),a=""),{__TYPE__:f.baggageEntryMetadataSymbol,toString:()=>a}}},67:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.context=void 0,b.context=c(491).ContextAPI.getInstance()},223:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopContextManager=void 0;let d=c(780);class e{active(){return d.ROOT_CONTEXT}with(a,b,c,...d){return b.call(c,...d)}bind(a,b){return b}enable(){return this}disable(){return this}}b.NoopContextManager=e},780:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ROOT_CONTEXT=b.createContextKey=void 0,b.createContextKey=function(a){return Symbol.for(a)};class c{constructor(a){let b=this;b._currentContext=a?new Map(a):new Map,b.getValue=a=>b._currentContext.get(a),b.setValue=(a,d)=>{let e=new c(b._currentContext);return e._currentContext.set(a,d),e},b.deleteValue=a=>{let d=new c(b._currentContext);return d._currentContext.delete(a),d}}}b.ROOT_CONTEXT=new c},506:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.diag=void 0,b.diag=c(930).DiagAPI.instance()},56:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagComponentLogger=void 0;let d=c(172);class e{constructor(a){this._namespace=a.namespace||"DiagComponentLogger"}debug(...a){return f("debug",this._namespace,a)}error(...a){return f("error",this._namespace,a)}info(...a){return f("info",this._namespace,a)}warn(...a){return f("warn",this._namespace,a)}verbose(...a){return f("verbose",this._namespace,a)}}function f(a,b,c){let e=(0,d.getGlobal)("diag");if(e)return c.unshift(b),e[a](...c)}b.DiagComponentLogger=e},972:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagConsoleLogger=void 0;let c=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class d{constructor(){for(let a=0;a<c.length;a++)this[c[a].n]=function(a){return function(...b){if(console){let c=console[a];if("function"!=typeof c&&(c=console.log),"function"==typeof c)return c.apply(console,b)}}}(c[a].c)}}b.DiagConsoleLogger=d},912:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createLogLevelDiagLogger=void 0;let d=c(957);b.createLogLevelDiagLogger=function(a,b){function c(c,d){let e=b[c];return"function"==typeof e&&a>=d?e.bind(b):function(){}}return a<d.DiagLogLevel.NONE?a=d.DiagLogLevel.NONE:a>d.DiagLogLevel.ALL&&(a=d.DiagLogLevel.ALL),b=b||{},{error:c("error",d.DiagLogLevel.ERROR),warn:c("warn",d.DiagLogLevel.WARN),info:c("info",d.DiagLogLevel.INFO),debug:c("debug",d.DiagLogLevel.DEBUG),verbose:c("verbose",d.DiagLogLevel.VERBOSE)}}},957:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagLogLevel=void 0,function(a){a[a.NONE=0]="NONE",a[a.ERROR=30]="ERROR",a[a.WARN=50]="WARN",a[a.INFO=60]="INFO",a[a.DEBUG=70]="DEBUG",a[a.VERBOSE=80]="VERBOSE",a[a.ALL=9999]="ALL"}(b.DiagLogLevel||(b.DiagLogLevel={}))},172:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.unregisterGlobal=b.getGlobal=b.registerGlobal=void 0;let d=c(200),e=c(521),f=c(130),g=e.VERSION.split(".")[0],h=Symbol.for(`opentelemetry.js.api.${g}`),i=d._globalThis;b.registerGlobal=function(a,b,c,d=!1){var f;let g=i[h]=null!=(f=i[h])?f:{version:e.VERSION};if(!d&&g[a]){let b=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${a}`);return c.error(b.stack||b.message),!1}if(g.version!==e.VERSION){let b=Error(`@opentelemetry/api: Registration of version v${g.version} for ${a} does not match previously registered API v${e.VERSION}`);return c.error(b.stack||b.message),!1}return g[a]=b,c.debug(`@opentelemetry/api: Registered a global for ${a} v${e.VERSION}.`),!0},b.getGlobal=function(a){var b,c;let d=null==(b=i[h])?void 0:b.version;if(d&&(0,f.isCompatible)(d))return null==(c=i[h])?void 0:c[a]},b.unregisterGlobal=function(a,b){b.debug(`@opentelemetry/api: Unregistering a global for ${a} v${e.VERSION}.`);let c=i[h];c&&delete c[a]}},130:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.isCompatible=b._makeCompatibilityCheck=void 0;let d=c(521),e=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function f(a){let b=new Set([a]),c=new Set,d=a.match(e);if(!d)return()=>!1;let f={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=f.prerelease)return function(b){return b===a};function g(a){return c.add(a),!1}return function(a){if(b.has(a))return!0;if(c.has(a))return!1;let d=a.match(e);if(!d)return g(a);let h={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=h.prerelease||f.major!==h.major)return g(a);if(0===f.major)return f.minor===h.minor&&f.patch<=h.patch?(b.add(a),!0):g(a);return f.minor<=h.minor?(b.add(a),!0):g(a)}}b._makeCompatibilityCheck=f,b.isCompatible=f(d.VERSION)},886:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.metrics=void 0,b.metrics=c(653).MetricsAPI.getInstance()},901:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ValueType=void 0,function(a){a[a.INT=0]="INT",a[a.DOUBLE=1]="DOUBLE"}(b.ValueType||(b.ValueType={}))},102:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createNoopMeter=b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=b.NOOP_OBSERVABLE_GAUGE_METRIC=b.NOOP_OBSERVABLE_COUNTER_METRIC=b.NOOP_UP_DOWN_COUNTER_METRIC=b.NOOP_HISTOGRAM_METRIC=b.NOOP_COUNTER_METRIC=b.NOOP_METER=b.NoopObservableUpDownCounterMetric=b.NoopObservableGaugeMetric=b.NoopObservableCounterMetric=b.NoopObservableMetric=b.NoopHistogramMetric=b.NoopUpDownCounterMetric=b.NoopCounterMetric=b.NoopMetric=b.NoopMeter=void 0;class c{constructor(){}createHistogram(a,c){return b.NOOP_HISTOGRAM_METRIC}createCounter(a,c){return b.NOOP_COUNTER_METRIC}createUpDownCounter(a,c){return b.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(a,c){return b.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(a,c){return b.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(a,c){return b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(a,b){}removeBatchObservableCallback(a){}}b.NoopMeter=c;class d{}b.NoopMetric=d;class e extends d{add(a,b){}}b.NoopCounterMetric=e;class f extends d{add(a,b){}}b.NoopUpDownCounterMetric=f;class g extends d{record(a,b){}}b.NoopHistogramMetric=g;class h{addCallback(a){}removeCallback(a){}}b.NoopObservableMetric=h;class i extends h{}b.NoopObservableCounterMetric=i;class j extends h{}b.NoopObservableGaugeMetric=j;class k extends h{}b.NoopObservableUpDownCounterMetric=k,b.NOOP_METER=new c,b.NOOP_COUNTER_METRIC=new e,b.NOOP_HISTOGRAM_METRIC=new g,b.NOOP_UP_DOWN_COUNTER_METRIC=new f,b.NOOP_OBSERVABLE_COUNTER_METRIC=new i,b.NOOP_OBSERVABLE_GAUGE_METRIC=new j,b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new k,b.createNoopMeter=function(){return b.NOOP_METER}},660:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NOOP_METER_PROVIDER=b.NoopMeterProvider=void 0;let d=c(102);class e{getMeter(a,b,c){return d.NOOP_METER}}b.NoopMeterProvider=e,b.NOOP_METER_PROVIDER=new e},200:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(46),b)},651:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b._globalThis=void 0,b._globalThis="object"==typeof globalThis?globalThis:c.g},46:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(651),b)},939:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.propagation=void 0,b.propagation=c(181).PropagationAPI.getInstance()},874:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTextMapPropagator=void 0;class c{inject(a,b){}extract(a,b){return a}fields(){return[]}}b.NoopTextMapPropagator=c},194:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.defaultTextMapSetter=b.defaultTextMapGetter=void 0,b.defaultTextMapGetter={get(a,b){if(null!=a)return a[b]},keys:a=>null==a?[]:Object.keys(a)},b.defaultTextMapSetter={set(a,b,c){null!=a&&(a[b]=c)}}},845:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.trace=void 0,b.trace=c(997).TraceAPI.getInstance()},403:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NonRecordingSpan=void 0;let d=c(476);class e{constructor(a=d.INVALID_SPAN_CONTEXT){this._spanContext=a}spanContext(){return this._spanContext}setAttribute(a,b){return this}setAttributes(a){return this}addEvent(a,b){return this}setStatus(a){return this}updateName(a){return this}end(a){}isRecording(){return!1}recordException(a,b){}}b.NonRecordingSpan=e},614:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracer=void 0;let d=c(491),e=c(607),f=c(403),g=c(139),h=d.ContextAPI.getInstance();class i{startSpan(a,b,c=h.active()){var d;if(null==b?void 0:b.root)return new f.NonRecordingSpan;let i=c&&(0,e.getSpanContext)(c);return"object"==typeof(d=i)&&"string"==typeof d.spanId&&"string"==typeof d.traceId&&"number"==typeof d.traceFlags&&(0,g.isSpanContextValid)(i)?new f.NonRecordingSpan(i):new f.NonRecordingSpan}startActiveSpan(a,b,c,d){let f,g,i;if(arguments.length<2)return;2==arguments.length?i=b:3==arguments.length?(f=b,i=c):(f=b,g=c,i=d);let j=null!=g?g:h.active(),k=this.startSpan(a,f,j),l=(0,e.setSpan)(j,k);return h.with(l,i,void 0,k)}}b.NoopTracer=i},124:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracerProvider=void 0;let d=c(614);class e{getTracer(a,b,c){return new d.NoopTracer}}b.NoopTracerProvider=e},125:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracer=void 0;let d=new(c(614)).NoopTracer;class e{constructor(a,b,c,d){this._provider=a,this.name=b,this.version=c,this.options=d}startSpan(a,b,c){return this._getTracer().startSpan(a,b,c)}startActiveSpan(a,b,c,d){let e=this._getTracer();return Reflect.apply(e.startActiveSpan,e,arguments)}_getTracer(){if(this._delegate)return this._delegate;let a=this._provider.getDelegateTracer(this.name,this.version,this.options);return a?(this._delegate=a,this._delegate):d}}b.ProxyTracer=e},846:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracerProvider=void 0;let d=c(125),e=new(c(124)).NoopTracerProvider;class f{getTracer(a,b,c){var e;return null!=(e=this.getDelegateTracer(a,b,c))?e:new d.ProxyTracer(this,a,b,c)}getDelegate(){var a;return null!=(a=this._delegate)?a:e}setDelegate(a){this._delegate=a}getDelegateTracer(a,b,c){var d;return null==(d=this._delegate)?void 0:d.getTracer(a,b,c)}}b.ProxyTracerProvider=f},996:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SamplingDecision=void 0,function(a){a[a.NOT_RECORD=0]="NOT_RECORD",a[a.RECORD=1]="RECORD",a[a.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(b.SamplingDecision||(b.SamplingDecision={}))},607:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.getSpanContext=b.setSpanContext=b.deleteSpan=b.setSpan=b.getActiveSpan=b.getSpan=void 0;let d=c(780),e=c(403),f=c(491),g=(0,d.createContextKey)("OpenTelemetry Context Key SPAN");function h(a){return a.getValue(g)||void 0}function i(a,b){return a.setValue(g,b)}b.getSpan=h,b.getActiveSpan=function(){return h(f.ContextAPI.getInstance().active())},b.setSpan=i,b.deleteSpan=function(a){return a.deleteValue(g)},b.setSpanContext=function(a,b){return i(a,new e.NonRecordingSpan(b))},b.getSpanContext=function(a){var b;return null==(b=h(a))?void 0:b.spanContext()}},325:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceStateImpl=void 0;let d=c(564);class e{constructor(a){this._internalState=new Map,a&&this._parse(a)}set(a,b){let c=this._clone();return c._internalState.has(a)&&c._internalState.delete(a),c._internalState.set(a,b),c}unset(a){let b=this._clone();return b._internalState.delete(a),b}get(a){return this._internalState.get(a)}serialize(){return this._keys().reduce((a,b)=>(a.push(b+"="+this.get(b)),a),[]).join(",")}_parse(a){!(a.length>512)&&(this._internalState=a.split(",").reverse().reduce((a,b)=>{let c=b.trim(),e=c.indexOf("=");if(-1!==e){let f=c.slice(0,e),g=c.slice(e+1,b.length);(0,d.validateKey)(f)&&(0,d.validateValue)(g)&&a.set(f,g)}return a},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let a=new e;return a._internalState=new Map(this._internalState),a}}b.TraceStateImpl=e},564:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.validateValue=b.validateKey=void 0;let c="[_0-9a-z-*/]",d=`[a-z]${c}{0,255}`,e=`[a-z0-9]${c}{0,240}@[a-z]${c}{0,13}`,f=RegExp(`^(?:${d}|${e})$`),g=/^[ -~]{0,255}[!-~]$/,h=/,|=/;b.validateKey=function(a){return f.test(a)},b.validateValue=function(a){return g.test(a)&&!h.test(a)}},98:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createTraceState=void 0;let d=c(325);b.createTraceState=function(a){return new d.TraceStateImpl(a)}},476:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.INVALID_SPAN_CONTEXT=b.INVALID_TRACEID=b.INVALID_SPANID=void 0;let d=c(475);b.INVALID_SPANID="0000000000000000",b.INVALID_TRACEID="00000000000000000000000000000000",b.INVALID_SPAN_CONTEXT={traceId:b.INVALID_TRACEID,spanId:b.INVALID_SPANID,traceFlags:d.TraceFlags.NONE}},357:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanKind=void 0,function(a){a[a.INTERNAL=0]="INTERNAL",a[a.SERVER=1]="SERVER",a[a.CLIENT=2]="CLIENT",a[a.PRODUCER=3]="PRODUCER",a[a.CONSUMER=4]="CONSUMER"}(b.SpanKind||(b.SpanKind={}))},139:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.wrapSpanContext=b.isSpanContextValid=b.isValidSpanId=b.isValidTraceId=void 0;let d=c(476),e=c(403),f=/^([0-9a-f]{32})$/i,g=/^[0-9a-f]{16}$/i;function h(a){return f.test(a)&&a!==d.INVALID_TRACEID}function i(a){return g.test(a)&&a!==d.INVALID_SPANID}b.isValidTraceId=h,b.isValidSpanId=i,b.isSpanContextValid=function(a){return h(a.traceId)&&i(a.spanId)},b.wrapSpanContext=function(a){return new e.NonRecordingSpan(a)}},847:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanStatusCode=void 0,function(a){a[a.UNSET=0]="UNSET",a[a.OK=1]="OK",a[a.ERROR=2]="ERROR"}(b.SpanStatusCode||(b.SpanStatusCode={}))},475:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceFlags=void 0,function(a){a[a.NONE=0]="NONE",a[a.SAMPLED=1]="SAMPLED"}(b.TraceFlags||(b.TraceFlags={}))},521:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.VERSION=void 0,b.VERSION="1.6.0"}},d={};function e(a){var c=d[a];if(void 0!==c)return c.exports;var f=d[a]={exports:{}},g=!0;try{b[a].call(f.exports,f,f.exports,e),g=!1}finally{g&&delete d[a]}return f.exports}e.ab="//";var f={};(()=>{Object.defineProperty(f,"__esModule",{value:!0}),f.trace=f.propagation=f.metrics=f.diag=f.context=f.INVALID_SPAN_CONTEXT=f.INVALID_TRACEID=f.INVALID_SPANID=f.isValidSpanId=f.isValidTraceId=f.isSpanContextValid=f.createTraceState=f.TraceFlags=f.SpanStatusCode=f.SpanKind=f.SamplingDecision=f.ProxyTracerProvider=f.ProxyTracer=f.defaultTextMapSetter=f.defaultTextMapGetter=f.ValueType=f.createNoopMeter=f.DiagLogLevel=f.DiagConsoleLogger=f.ROOT_CONTEXT=f.createContextKey=f.baggageEntryMetadataFromString=void 0;var a=e(369);Object.defineProperty(f,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return a.baggageEntryMetadataFromString}});var b=e(780);Object.defineProperty(f,"createContextKey",{enumerable:!0,get:function(){return b.createContextKey}}),Object.defineProperty(f,"ROOT_CONTEXT",{enumerable:!0,get:function(){return b.ROOT_CONTEXT}});var c=e(972);Object.defineProperty(f,"DiagConsoleLogger",{enumerable:!0,get:function(){return c.DiagConsoleLogger}});var d=e(957);Object.defineProperty(f,"DiagLogLevel",{enumerable:!0,get:function(){return d.DiagLogLevel}});var g=e(102);Object.defineProperty(f,"createNoopMeter",{enumerable:!0,get:function(){return g.createNoopMeter}});var h=e(901);Object.defineProperty(f,"ValueType",{enumerable:!0,get:function(){return h.ValueType}});var i=e(194);Object.defineProperty(f,"defaultTextMapGetter",{enumerable:!0,get:function(){return i.defaultTextMapGetter}}),Object.defineProperty(f,"defaultTextMapSetter",{enumerable:!0,get:function(){return i.defaultTextMapSetter}});var j=e(125);Object.defineProperty(f,"ProxyTracer",{enumerable:!0,get:function(){return j.ProxyTracer}});var k=e(846);Object.defineProperty(f,"ProxyTracerProvider",{enumerable:!0,get:function(){return k.ProxyTracerProvider}});var l=e(996);Object.defineProperty(f,"SamplingDecision",{enumerable:!0,get:function(){return l.SamplingDecision}});var m=e(357);Object.defineProperty(f,"SpanKind",{enumerable:!0,get:function(){return m.SpanKind}});var n=e(847);Object.defineProperty(f,"SpanStatusCode",{enumerable:!0,get:function(){return n.SpanStatusCode}});var o=e(475);Object.defineProperty(f,"TraceFlags",{enumerable:!0,get:function(){return o.TraceFlags}});var p=e(98);Object.defineProperty(f,"createTraceState",{enumerable:!0,get:function(){return p.createTraceState}});var q=e(139);Object.defineProperty(f,"isSpanContextValid",{enumerable:!0,get:function(){return q.isSpanContextValid}}),Object.defineProperty(f,"isValidTraceId",{enumerable:!0,get:function(){return q.isValidTraceId}}),Object.defineProperty(f,"isValidSpanId",{enumerable:!0,get:function(){return q.isValidSpanId}});var r=e(476);Object.defineProperty(f,"INVALID_SPANID",{enumerable:!0,get:function(){return r.INVALID_SPANID}}),Object.defineProperty(f,"INVALID_TRACEID",{enumerable:!0,get:function(){return r.INVALID_TRACEID}}),Object.defineProperty(f,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return r.INVALID_SPAN_CONTEXT}});let s=e(67);Object.defineProperty(f,"context",{enumerable:!0,get:function(){return s.context}});let t=e(506);Object.defineProperty(f,"diag",{enumerable:!0,get:function(){return t.diag}});let u=e(886);Object.defineProperty(f,"metrics",{enumerable:!0,get:function(){return u.metrics}});let v=e(939);Object.defineProperty(f,"propagation",{enumerable:!0,get:function(){return v.propagation}});let w=e(845);Object.defineProperty(f,"trace",{enumerable:!0,get:function(){return w.trace}}),f.default={context:s.context,diag:t.diag,metrics:u.metrics,propagation:v.propagation,trace:w.trace}})(),a.exports=f})()}},a=>{var b=a(a.s=17);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=b}]);
//# sourceMappingURL=middleware.js.map