"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[173],{1362:(e,t,n)=>{n.d(t,{D:()=>s,ThemeProvider:()=>c});var r=n(2115),o=(e,t,n,r,o,i,l,a)=>{let u=document.documentElement,s=["light","dark"];function c(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?o.map(e=>i[e]||e):o;n?(u.classList.remove(...r),u.classList.add(i&&i[t]?i[t]:t)):u.setAttribute(e,t)}),n=t,a&&s.includes(n)&&(u.style.colorScheme=n)}if(r)c(r);else try{let e=localStorage.getItem(t)||n,r=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(r)}catch(e){}},i=["light","dark"],l="(prefers-color-scheme: dark)",a=r.createContext(void 0),u={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(a))?e:u},c=e=>r.useContext(a)?r.createElement(r.Fragment,null,e.children):r.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:u=!0,storageKey:s="theme",themes:c=d,defaultTheme:f=o?"system":"light",attribute:g="data-theme",value:y,children:w,nonce:b,scriptProps:x}=e,[E,R]=r.useState(()=>h(s,f)),[C,A]=r.useState(()=>"system"===E?v():E),S=y?Object.values(y):c,M=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=v());let r=y?y[t]:t,l=n?m(b):null,a=document.documentElement,s=e=>{"class"===e?(a.classList.remove(...S),r&&a.classList.add(r)):e.startsWith("data-")&&(r?a.setAttribute(e,r):a.removeAttribute(e))};if(Array.isArray(g)?g.forEach(s):s(g),u){let e=i.includes(f)?f:null,n=i.includes(t)?t:e;a.style.colorScheme=n}null==l||l()},[b]),k=r.useCallback(e=>{let t="function"==typeof e?e(E):e;R(t);try{localStorage.setItem(s,t)}catch(e){}},[E]),T=r.useCallback(e=>{A(v(e)),"system"===E&&o&&!t&&M("system")},[E,t]);r.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(T),T(e),()=>e.removeListener(T)},[T]),r.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?R(e.newValue):k(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[k]),r.useEffect(()=>{M(null!=t?t:E)},[t,E]);let L=r.useMemo(()=>({theme:E,setTheme:k,forcedTheme:t,resolvedTheme:"system"===E?C:E,themes:o?[...c,"system"]:c,systemTheme:o?C:void 0}),[E,k,t,C,o,c]);return r.createElement(a.Provider,{value:L},r.createElement(p,{forcedTheme:t,storageKey:s,attribute:g,enableSystem:o,enableColorScheme:u,defaultTheme:f,value:y,themes:c,nonce:b,scriptProps:x}),w)},p=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:i,enableSystem:l,enableColorScheme:a,defaultTheme:u,value:s,themes:c,nonce:d,scriptProps:f}=e,p=JSON.stringify([i,n,u,t,c,s,l,a]).slice(1,-1);return r.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),h=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},2049:(e,t,n)=>{n.d(t,{H_:()=>r1,UC:()=>rq,YJ:()=>rJ,q7:()=>r0,VF:()=>r5,JU:()=>rQ,ZL:()=>rZ,z6:()=>r2,hN:()=>r9,bL:()=>rY,wv:()=>r4,Pb:()=>r6,G5:()=>r7,ZP:()=>r3,l9:()=>r$});var r,o,i,l,a=n(2115),u=n.t(a,2);function s(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(6101),d=n(5155);function f(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let r=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=a.createContext(r),i=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,u=n?.[e]?.[i]||o,s=a.useMemo(()=>l,Object.values(l));return(0,d.jsx)(u.Provider,{value:s,children:r})};return l.displayName=t+"Provider",[l,function(n,l){let u=l?.[e]?.[i]||o,s=a.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var p=globalThis?.document?a.useLayoutEffect:()=>{},h=u[" useInsertionEffect ".trim().toString()]||p;function m({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return h(()=>{i.current=t},[t]),a.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,a.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else i(t)},[u,e,i,l])]}Symbol("RADIX:SYNC_STATE");var v=n(3655);function g(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function y(e,t){var n=g(e,t,"get");return n.get?n.get.call(e):n.value}function w(e,t,n){var r=g(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var b=n(9708);function x(e){let t=e+"CollectionProvider",[n,r]=f(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let u=e+"CollectionSlot",s=(0,b.TL)(u),p=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(u,n),l=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(s,{ref:l,children:r})});p.displayName=u;let h=e+"CollectionItemSlot",m="data-radix-collection-item",v=(0,b.TL)(h),g=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,c.s)(t,l),s=i(h,n);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,d.jsx)(v,{...{[m]:""},ref:u,children:r})});return g.displayName=h,[{Provider:l,Slot:p,ItemSlot:g},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var E=new WeakMap;function R(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=C(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function C(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return E.get(this)&&(this.has(e)?y(this,o)[y(this,o).indexOf(e)]=e:y(this,o).push(e)),super.set(e,t),this}insert(e,t,n){let r,i=this.has(t),l=y(this,o).length,a=C(e),u=a>=0?a:l+a,s=u<0||u>=l?-1:u;if(s===this.size||i&&s===this.size-1||-1===s)return this.set(t,n),this;let c=this.size+ +!i;a<0&&u++;let d=[...y(this,o)],f=!1;for(let e=u;e<c;e++)if(u===e){let o=d[e];d[e]===t&&(o=d[e+1]),i&&this.delete(t),r=this.get(o),this.set(t,n)}else{f||d[e-1]!==t||(f=!0);let n=d[f?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=y(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=y(this,o).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=y(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=y(this,o).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return w(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&y(this,o).splice(y(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=R(y(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=R(y(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return y(this,o).indexOf(e)}keyAt(e){return R(y(this,o),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,l=null!=o?o:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(r,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,o,{writable:!0,value:void 0}),w(this,o,[...super.keys()]),E.set(this,!0)}};var A=a.createContext(void 0);function S(e){let t=a.useContext(A);return e||t||"ltr"}function M(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var k="dismissableLayer.update",T=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),L=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:p,onDismiss:h,...m}=e,g=a.useContext(T),[y,w]=a.useState(null),b=null!=(r=null==y?void 0:y.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=a.useState({}),E=(0,c.s)(t,e=>w(e)),R=Array.from(g.layers),[C]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),A=R.indexOf(C),S=y?R.indexOf(y):-1,L=g.layersWithOutsidePointerEventsDisabled.size>0,N=S>=A,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=M(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){P("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));N&&!n&&(null==u||u(e),null==p||p(e),e.defaultPrevented||null==h||h())},b),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=M(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&P("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...g.branches].some(e=>e.contains(t))&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==h||h())},b);return!function(e,t=globalThis?.document){let n=M(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===g.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},b),a.useEffect(()=>{if(y)return o&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(i=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(y)),g.layers.add(y),O(),()=>{o&&1===g.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=i)}},[y,b,o,g]),a.useEffect(()=>()=>{y&&(g.layers.delete(y),g.layersWithOutsidePointerEventsDisabled.delete(y),O())},[y,g]),a.useEffect(()=>{let e=()=>x({});return document.addEventListener(k,e),()=>document.removeEventListener(k,e)},[]),(0,d.jsx)(v.sG.div,{...m,ref:E,style:{pointerEvents:L?N?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,j.onFocusCapture),onBlurCapture:s(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,D.onPointerDownCapture)})});function O(){let e=new CustomEvent(k);document.dispatchEvent(e)}function P(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,v.hO)(i,l):i.dispatchEvent(l)}L.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(T),r=a.useRef(null),o=(0,c.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(v.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var N=0;function D(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var j="focusScope.autoFocusOnMount",I="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},F=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,s]=a.useState(null),f=M(o),p=M(i),h=a.useRef(null),m=(0,c.s)(t,e=>s(e)),g=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(g.paused||!u)return;let t=e.target;u.contains(t)?h.current=t:K(h.current,{select:!0})},t=function(e){if(g.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||K(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&K(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,g.paused]),a.useEffect(()=>{if(u){B.add(g);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(j,_);u.addEventListener(j,f),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(K(r,{select:t}),document.activeElement!==n)return}(W(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&K(u))}return()=>{u.removeEventListener(j,f),setTimeout(()=>{let t=new CustomEvent(I,_);u.addEventListener(I,p),u.dispatchEvent(t),t.defaultPrevented||K(null!=e?e:document.body,{select:!0}),u.removeEventListener(I,p),B.remove(g)},0)}}},[u,f,p,g]);let y=a.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=W(e);return[z(t,e),z(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&K(i,{select:!0})):(e.preventDefault(),n&&K(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,d.jsx)(v.sG.div,{tabIndex:-1,...l,ref:m,onKeyDown:y})});function W(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function z(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function K(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}F.displayName="FocusScope";var B=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=H(e,t)).unshift(t)},remove(t){var n;null==(n=(e=H(e,t))[0])||n.resume()}}}();function H(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var G=u[" useId ".trim().toString()]||(()=>void 0),U=0;function V(e){let[t,n]=a.useState(G());return p(()=>{e||n(e=>e??String(U++))},[e]),e||(t?`radix-${t}`:"")}let X=["top","right","bottom","left"],Y=Math.min,$=Math.max,Z=Math.round,q=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function et(e,t){return"function"==typeof e?e(t):e}function en(e){return e.split("-")[0]}function er(e){return e.split("-")[1]}function eo(e){return"x"===e?"y":"x"}function ei(e){return"y"===e?"height":"width"}let el=new Set(["top","bottom"]);function ea(e){return el.has(en(e))?"y":"x"}function eu(e){return e.replace(/start|end/g,e=>ee[e])}let es=["left","right"],ec=["right","left"],ed=["top","bottom"],ef=["bottom","top"];function ep(e){return e.replace(/left|right|bottom|top/g,e=>Q[e])}function eh(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function em(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ev(e,t,n){let r,{reference:o,floating:i}=e,l=ea(t),a=eo(ea(t)),u=ei(a),s=en(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(er(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let eg=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=ev(s,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=ev(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function ey(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=et(t,e),h=eh(p),m=a[f?"floating"===d?"reference":"floating":d],v=em(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=em(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function ew(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eb(e){return X.some(t=>e[t]>=0)}let ex=new Set(["left","top"]);async function eE(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=en(n),a=er(n),u="y"===ea(n),s=ex.has(l)?-1:1,c=i&&u?-1:1,d=et(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*c,y:f*s}:{x:f*s,y:p*c}}function eR(){return"undefined"!=typeof window}function eC(e){return eM(e)?(e.nodeName||"").toLowerCase():"#document"}function eA(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eS(e){var t;return null==(t=(eM(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eM(e){return!!eR()&&(e instanceof Node||e instanceof eA(e).Node)}function ek(e){return!!eR()&&(e instanceof Element||e instanceof eA(e).Element)}function eT(e){return!!eR()&&(e instanceof HTMLElement||e instanceof eA(e).HTMLElement)}function eL(e){return!!eR()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eA(e).ShadowRoot)}let eO=new Set(["inline","contents"]);function eP(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eH(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!eO.has(o)}let eN=new Set(["table","td","th"]),eD=[":popover-open",":modal"];function ej(e){return eD.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eI=["transform","translate","scale","rotate","perspective"],e_=["transform","translate","scale","rotate","perspective","filter"],eF=["paint","layout","strict","content"];function eW(e){let t=ez(),n=ek(e)?eH(e):e;return eI.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||e_.some(e=>(n.willChange||"").includes(e))||eF.some(e=>(n.contain||"").includes(e))}function ez(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eK=new Set(["html","body","#document"]);function eB(e){return eK.has(eC(e))}function eH(e){return eA(e).getComputedStyle(e)}function eG(e){return ek(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eU(e){if("html"===eC(e))return e;let t=e.assignedSlot||e.parentNode||eL(e)&&e.host||eS(e);return eL(t)?t.host:t}function eV(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eU(t);return eB(n)?t.ownerDocument?t.ownerDocument.body:t.body:eT(n)&&eP(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eA(o);if(i){let e=eX(l);return t.concat(l,l.visualViewport||[],eP(o)?o:[],e&&n?eV(e):[])}return t.concat(o,eV(o,[],n))}function eX(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eY(e){let t=eH(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eT(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=Z(n)!==i||Z(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function e$(e){return ek(e)?e:e.contextElement}function eZ(e){let t=e$(e);if(!eT(t))return J(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eY(t),l=(i?Z(n.width):n.width)/r,a=(i?Z(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eq=J(0);function eJ(e){let t=eA(e);return ez()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eq}function eQ(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=e$(e),a=J(1);t&&(r?ek(r)&&(a=eZ(r)):a=eZ(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eA(l))&&o)?eJ(l):J(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=eA(l),t=r&&ek(r)?eA(r):r,n=e,o=eX(n);for(;o&&r&&t!==n;){let e=eZ(o),t=o.getBoundingClientRect(),r=eH(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,d*=e.x,f*=e.y,s+=i,c+=l,o=eX(n=eA(o))}}return em({width:d,height:f,x:s,y:c})}function e0(e,t){let n=eG(e).scrollLeft;return t?t.left+n:eQ(eS(e)).left+n}function e1(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:e0(e,r)),y:r.top+t.scrollTop}}let e2=new Set(["absolute","fixed"]);function e9(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eA(e),r=eS(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=ez();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eS(e),n=eG(e),r=e.ownerDocument.body,o=$(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=$(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+e0(e),a=-n.scrollTop;return"rtl"===eH(r).direction&&(l+=$(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eS(e));else if(ek(t))r=function(e,t){let n=eQ(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eT(e)?eZ(e):J(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eJ(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return em(r)}function e5(e){return"static"===eH(e).position}function e4(e,t){if(!eT(e)||"fixed"===eH(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eS(e)===n&&(n=n.ownerDocument.body),n}function e6(e,t){var n;let r=eA(e);if(ej(e))return r;if(!eT(e)){let t=eU(e);for(;t&&!eB(t);){if(ek(t)&&!e5(t))return t;t=eU(t)}return r}let o=e4(e,t);for(;o&&(n=o,eN.has(eC(n)))&&e5(o);)o=e4(o,t);return o&&eB(o)&&e5(o)&&!eW(o)?r:o||function(e){let t=eU(e);for(;eT(t)&&!eB(t);){if(eW(t))return t;if(ej(t))break;t=eU(t)}return null}(e)||r}let e3=async function(e){let t=this.getOffsetParent||e6,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eT(t),o=eS(t),i="fixed"===n,l=eQ(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=J(0);if(r||!r&&!i)if(("body"!==eC(t)||eP(o))&&(a=eG(t)),r){let e=eQ(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=e0(o));i&&!r&&o&&(u.x=e0(o));let s=!o||r||i?J(0):e1(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e7={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eS(r),a=!!t&&ej(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=J(1),c=J(0),d=eT(r);if((d||!d&&!i)&&(("body"!==eC(r)||eP(l))&&(u=eG(r)),eT(r))){let e=eQ(r);s=eZ(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!l||d||i?J(0):e1(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+f.x,y:n.y*s.y-u.scrollTop*s.y+c.y+f.y}},getDocumentElement:eS,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ej(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eV(e,[],!1).filter(e=>ek(e)&&"body"!==eC(e)),o=null,i="fixed"===eH(e).position,l=i?eU(e):e;for(;ek(l)&&!eB(l);){let t=eH(l),n=eW(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e2.has(o.position)||eP(l)&&!n&&function e(t,n){let r=eU(t);return!(r===n||!ek(r)||eB(r))&&("fixed"===eH(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eU(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=e9(t,n,o);return e.top=$(r.top,e.top),e.right=Y(r.right,e.right),e.bottom=Y(r.bottom,e.bottom),e.left=$(r.left,e.left),e},e9(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e6,getElementRects:e3,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eY(e);return{width:t,height:n}},getScale:eZ,isElement:ek,isRTL:function(e){return"rtl"===eH(e).direction}};function e8(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let te=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=et(e,t)||{};if(null==s)return{};let d=eh(c),f={x:n,y:r},p=eo(ea(o)),h=ei(p),m=await l.getDimensions(s),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),x=b?b[g]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[g]||i.floating[h]);let E=x/2-m[h]/2-1,R=Y(d[v?"top":"left"],E),C=Y(d[v?"bottom":"right"],E),A=x-m[h]-C,S=x/2-m[h]/2+(y/2-w/2),M=$(R,Y(S,A)),k=!u.arrow&&null!=er(o)&&S!==M&&i.reference[h]/2-(S<R?R:C)-m[h]/2<0,T=k?S<R?S-R:S-A:0;return{[p]:f[p]+T,data:{[p]:M,centerOffset:S-M-T,...k&&{alignmentOffset:T}},reset:k}}});var tt=n(7650),tn="undefined"!=typeof document?a.useLayoutEffect:function(){};function tr(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!tr(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!tr(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function to(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ti(e,t){let n=to(e);return Math.round(t*n)/n}function tl(e){let t=a.useRef(e);return tn(()=>{t.current=e}),t}var ta=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,d.jsx)(v.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ta.displayName="Arrow";var tu="Popper",[ts,tc]=f(tu),[td,tf]=ts(tu),tp=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,d.jsx)(td,{scope:t,anchor:r,onAnchorChange:o,children:n})};tp.displayName=tu;var th="PopperAnchor",tm=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tf(th,n),l=a.useRef(null),u=(0,c.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,d.jsx)(v.sG.div,{...o,ref:u})});tm.displayName=th;var tv="PopperContent",[tg,ty]=ts(tv),tw=a.forwardRef((e,t)=>{var n,r,o,i,l,u,s,f;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:b=0,avoidCollisions:x=!0,collisionBoundary:E=[],collisionPadding:R=0,sticky:C="partial",hideWhenDetached:A=!1,updatePositionStrategy:S="optimized",onPlaced:k,...T}=e,L=tf(tv,h),[O,P]=a.useState(null),N=(0,c.s)(t,e=>P(e)),[D,j]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return p(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(D),_=null!=(s=null==I?void 0:I.width)?s:0,F=null!=(f=null==I?void 0:I.height)?f:0,W="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},z=Array.isArray(E)?E:[E],K=z.length>0,B={padding:W,boundary:z.filter(tR),altBoundary:K},{refs:H,floatingStyles:G,placement:U,isPositioned:V,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=a.useState(r);tr(p,r)||h(r);let[m,v]=a.useState(null),[g,y]=a.useState(null),w=a.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=a.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=i||m,E=l||g,R=a.useRef(null),C=a.useRef(null),A=a.useRef(d),S=null!=s,M=tl(s),k=tl(o),T=tl(c),L=a.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),((e,t,n)=>{let r=new Map,o={platform:e7,...n},i={...o.platform,_c:r};return eg(e,t,{...o,platform:i})})(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};O.current&&!tr(A.current,t)&&(A.current=t,tt.flushSync(()=>{f(t)}))})},[p,t,n,k,T]);tn(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let O=a.useRef(!1);tn(()=>(O.current=!0,()=>{O.current=!1}),[]),tn(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(M.current)return M.current(x,E,L);L()}},[x,E,L,M,S]);let P=a.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),N=a.useMemo(()=>({reference:x,floating:E}),[x,E]),D=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=ti(N.floating,d.x),r=ti(N.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...to(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,N.floating,d.x,d.y]);return a.useMemo(()=>({...d,update:L,refs:P,elements:N,floatingStyles:D}),[d,L,P,N,D])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=e$(e),d=i||l?[...c?eV(c):[],...eV(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=c&&u?function(e,t){let n,r=null,o=eS(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=s;if(a||t(),!f||!p)return;let h=q(d),m=q(o.clientWidth-(c+f)),v={rootMargin:-h+"px "+-m+"px "+-q(o.clientHeight-(d+p))+"px "+-q(c)+"px",threshold:$(0,Y(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e8(s,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!s&&h.observe(c),h.observe(t));let m=s?eQ(e):null;return s&&function t(){let r=eQ(e);m&&!e8(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===S})},elements:{reference:L.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await eE(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:g+F,alignmentAxis:w}),x&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=et(e,t),s={x:n,y:r},c=await ey(t,u),d=ea(en(o)),f=eo(d),p=s[f],h=s[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=$(n,Y(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=$(n,Y(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=et(e,t),c={x:n,y:r},d=ea(o),f=eo(d),p=c[f],h=c[d],m=et(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var g,y;let e="y"===f?"width":"height",t=ex.has(en(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}))():void 0,...B}),x&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=et(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=en(a),x=ea(c),E=en(c)===c,R=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[ep(c)]:function(e){let t=ep(e);return[eu(e),t,eu(t)]}(c)),A="none"!==g;!m&&A&&C.push(...function(e,t,n,r){let o=er(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?ec:es;return t?es:ec;case"left":case"right":return t?ed:ef;default:return[]}}(en(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(eu)))),i}(c,y,g,R));let S=[c,...C],M=await ey(t,w),k=[],T=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&k.push(M[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=er(e),o=eo(ea(e)),i=ei(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ep(l)),[l,ep(l)]}(a,s,R);k.push(M[e[0]],M[e[1]])}if(T=[...T,{placement:a,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=S[e];if(t&&("alignment"!==h||x===ea(t)||T.every(e=>e.overflows[0]>0&&ea(e.placement)===x)))return{data:{index:e,overflows:T},reset:{placement:t}};let n=null==(i=T.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=T.filter(e=>{if(A){let t=ea(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...B}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...d}=et(e,t),f=await ey(t,d),p=en(l),h=er(l),m="y"===ea(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,b=Y(g-f[o],y),x=Y(v-f[i],w),E=!t.middlewareData.shift,R=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!h){let e=$(f.left,0),t=$(f.right,0),n=$(f.top,0),r=$(f.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:$(f.left,f.right)):R=g-2*(0!==n||0!==r?n+r:$(f.top,f.bottom))}await c({...t,availableWidth:C,availableHeight:R});let A=await u.getDimensions(s.floating);return v!==A.width||g!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?te({element:n.current,padding:r}).fn(t):{}:n?te({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:D,padding:b}),tC({arrowWidth:_,arrowHeight:F}),A&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=et(e,t);switch(r){case"referenceHidden":{let e=ew(await ey(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:eb(e)}}}case"escaped":{let e=ew(await ey(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:eb(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...B})]}),[Z,J]=tA(U),Q=M(k);p(()=>{V&&(null==Q||Q())},[V,Q]);let ee=null==(n=X.arrow)?void 0:n.x,el=null==(r=X.arrow)?void 0:r.y,eh=(null==(o=X.arrow)?void 0:o.centerOffset)!==0,[em,ev]=a.useState();return p(()=>{O&&ev(window.getComputedStyle(O).zIndex)},[O]),(0,d.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:V?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:em,"--radix-popper-transform-origin":[null==(i=X.transformOrigin)?void 0:i.x,null==(l=X.transformOrigin)?void 0:l.y].join(" "),...(null==(u=X.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(tg,{scope:h,placedSide:Z,onArrowChange:j,arrowX:ee,arrowY:el,shouldHideArrow:eh,children:(0,d.jsx)(v.sG.div,{"data-side":Z,"data-align":J,...T,ref:N,style:{...T.style,animation:V?void 0:"none"}})})})});tw.displayName=tv;var tb="PopperArrow",tx={top:"bottom",right:"left",bottom:"top",left:"right"},tE=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ty(tb,n),i=tx[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(ta,{...r,ref:t,style:{...r.style,display:"block"}})})});function tR(e){return null!==e}tE.displayName=tb;var tC=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=tA(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function tA(e){let[t,n="center"]=e.split("-");return[t,n]}var tS=a.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,u]=a.useState(!1);p(()=>u(!0),[]);let s=o||l&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?tt.createPortal((0,d.jsx)(v.sG.div,{...i,ref:t}),s):null});tS.displayName="Portal";var tM=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=a.useState(),i=a.useRef(null),l=a.useRef(e),u=a.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return a.useEffect(()=>{let e=tk(i.current);u.current="mounted"===s?e:"none"},[s]),p(()=>{let t=i.current,n=l.current;if(n!==e){let r=u.current,o=tk(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),p(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=tk(i.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!l.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},a=e=>{e.target===r&&(u.current=tk(i.current))};return r.addEventListener("animationstart",a),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",a),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),i=(0,c.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?a.cloneElement(o,{ref:i}):null};function tk(e){return(null==e?void 0:e.animationName)||"none"}tM.displayName="Presence";var tT="rovingFocusGroup.onEntryFocus",tL={bubbles:!1,cancelable:!0},tO="RovingFocusGroup",[tP,tN,tD]=x(tO),[tj,tI]=f(tO,[tD]),[t_,tF]=tj(tO),tW=a.forwardRef((e,t)=>(0,d.jsx)(tP.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tP.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(tz,{...e,ref:t})})}));tW.displayName=tO;var tz=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:h=!1,...g}=e,y=a.useRef(null),w=(0,c.s)(t,y),b=S(i),[x,E]=m({prop:l,defaultProp:null!=u?u:null,onChange:f,caller:tO}),[R,C]=a.useState(!1),A=M(p),k=tN(n),T=a.useRef(!1),[L,O]=a.useState(0);return a.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(tT,A),()=>e.removeEventListener(tT,A)},[A]),(0,d.jsx)(t_,{scope:n,orientation:r,dir:b,loop:o,currentTabStopId:x,onItemFocus:a.useCallback(e=>E(e),[E]),onItemShiftTab:a.useCallback(()=>C(!0),[]),onFocusableItemAdd:a.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>O(e=>e-1),[]),children:(0,d.jsx)(v.sG.div,{tabIndex:R||0===L?-1:0,"data-orientation":r,...g,ref:w,style:{outline:"none",...e.style},onMouseDown:s(e.onMouseDown,()=>{T.current=!0}),onFocus:s(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(tT,tL);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);tG([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),h)}}T.current=!1}),onBlur:s(e.onBlur,()=>C(!1))})})}),tK="RovingFocusGroupItem",tB=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:l,...u}=e,c=V(),f=i||c,p=tF(tK,n),h=p.currentTabStopId===f,m=tN(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:w}=p;return a.useEffect(()=>{if(r)return g(),()=>y()},[r,g,y]),(0,d.jsx)(tP.ItemSlot,{scope:n,id:f,focusable:r,active:o,children:(0,d.jsx)(v.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:s(e.onMouseDown,e=>{r?p.onItemFocus(f):e.preventDefault()}),onFocus:s(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:s(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tH[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tG(n))}}),children:"function"==typeof l?l({isCurrentTabStop:h,hasTabStop:null!=w}):l})})});tB.displayName=tK;var tH={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tG(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tU=new WeakMap,tV=new WeakMap,tX={},tY=0,t$=function(e){return e&&(e.host||t$(e.parentNode))},tZ=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=t$(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tX[n]||(tX[n]=new WeakMap);var i=tX[n],l=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tU.get(e)||0)+1,s=(i.get(e)||0)+1;tU.set(e,u),i.set(e,s),l.push(e),1===u&&o&&tV.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tY++,function(){l.forEach(function(e){var t=tU.get(e)-1,o=i.get(e)-1;tU.set(e,t),i.set(e,o),t||(tV.has(e)||e.removeAttribute(r),tV.delete(e)),o||e.removeAttribute(n)}),--tY||(tU=new WeakMap,tU=new WeakMap,tV=new WeakMap,tX={})}},tq=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tZ(r,o,n,"aria-hidden")):function(){return null}},tJ=function(){return(tJ=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tQ(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var t0=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),t1="width-before-scroll-bar";function t2(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var t9="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,t5=new WeakMap;function t4(e){return e}var t6=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=t4),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tJ({async:!0,ssr:!1},e),o}(),t3=function(){},t7=a.forwardRef(function(e,t){var n,r,o,i,l=a.useRef(null),u=a.useState({onScrollCapture:t3,onWheelCapture:t3,onTouchMoveCapture:t3}),s=u[0],c=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,R=e.gapMode,C=tQ(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[l,t],r=function(e){return n.forEach(function(t){return t2(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,t9(function(){var e=t5.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||t2(e,null)}),r.forEach(function(e){t.has(e)||t2(e,o)})}t5.set(i,n)},[n]),i),S=tJ(tJ({},C),s);return a.createElement(a.Fragment,null,m&&a.createElement(g,{sideCar:t6,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:R}),d?a.cloneElement(a.Children.only(f),tJ(tJ({},S),{ref:A})):a.createElement(void 0===E?"div":E,tJ({},S,{className:p,ref:A}),f))});t7.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},t7.classNames={fullWidth:t1,zeroRight:t0};var t8=function(e){var t=e.sideCar,n=tQ(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tJ({},n))};t8.isSideCarExport=!0;var ne=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},nt=function(){var e=ne();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},nn=function(){var e=nt();return function(t){return e(t.styles,t.dynamic),null}},nr={left:0,top:0,right:0,gap:0},no=function(e){return parseInt(e||"",10)||0},ni=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[no(n),no(r),no(o)]},nl=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return nr;var t=ni(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},na=nn(),nu="data-scroll-locked",ns=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(nu,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(t0," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(t1," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(t0," .").concat(t0," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(t1," .").concat(t1," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(nu,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},nc=function(){var e=parseInt(document.body.getAttribute(nu)||"0",10);return isFinite(e)?e:0},nd=function(){a.useEffect(function(){return document.body.setAttribute(nu,(nc()+1).toString()),function(){var e=nc()-1;e<=0?document.body.removeAttribute(nu):document.body.setAttribute(nu,e.toString())}},[])},nf=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;nd();var i=a.useMemo(function(){return nl(o)},[o]);return a.createElement(na,{styles:ns(i,!t,o,n?"":"!important")})},np=!1;if("undefined"!=typeof window)try{var nh=Object.defineProperty({},"passive",{get:function(){return np=!0,!0}});window.addEventListener("test",nh,nh),window.removeEventListener("test",nh,nh)}catch(e){np=!1}var nm=!!np&&{passive:!1},nv=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ng=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ny(e,r)){var o=nw(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ny=function(e,t){return"v"===e?nv(t,"overflowY"):nv(t,"overflowX")},nw=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nb=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=nw(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&ny(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},nx=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},nE=function(e){return[e.deltaX,e.deltaY]},nR=function(e){return e&&"current"in e?e.current:e},nC=0,nA=[];let nS=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(nC++)[0],i=a.useState(nn)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nR),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nx(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=ng(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ng(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return nb(p,t,e,"h"===p?u:s,!0)},[]),s=a.useCallback(function(e){if(nA.length&&nA[nA.length-1]===i){var n="deltaY"in e?nE(e):nx(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nR).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=nx(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,nE(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,nx(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return nA.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,nm),document.addEventListener("touchmove",s,nm),document.addEventListener("touchstart",d,nm),function(){nA=nA.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,nm),document.removeEventListener("touchmove",s,nm),document.removeEventListener("touchstart",d,nm)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(nf,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},t6.useMedium(r),t8);var nM=a.forwardRef(function(e,t){return a.createElement(t7,tJ({},e,{ref:t,sideCar:nS}))});nM.classNames=t7.classNames;var nk=["Enter"," "],nT=["ArrowUp","PageDown","End"],nL=["ArrowDown","PageUp","Home",...nT],nO={ltr:[...nk,"ArrowRight"],rtl:[...nk,"ArrowLeft"]},nP={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nN="Menu",[nD,nj,nI]=x(nN),[n_,nF]=f(nN,[nI,tc,tI]),nW=tc(),nz=tI(),[nK,nB]=n_(nN),[nH,nG]=n_(nN),nU=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:l=!0}=e,u=nW(t),[s,c]=a.useState(null),f=a.useRef(!1),p=M(i),h=S(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,d.jsx)(tp,{...u,children:(0,d.jsx)(nK,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:c,children:(0,d.jsx)(nH,{scope:t,onClose:a.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:r})})})};nU.displayName=nN;var nV=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nW(n);return(0,d.jsx)(tm,{...o,...r,ref:t})});nV.displayName="MenuAnchor";var nX="MenuPortal",[nY,n$]=n_(nX,{forceMount:void 0}),nZ=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nB(nX,t);return(0,d.jsx)(nY,{scope:t,forceMount:n,children:(0,d.jsx)(tM,{present:n||i.open,children:(0,d.jsx)(tS,{asChild:!0,container:o,children:r})})})};nZ.displayName=nX;var nq="MenuContent",[nJ,nQ]=n_(nq),n0=a.forwardRef((e,t)=>{let n=n$(nq,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nB(nq,e.__scopeMenu),l=nG(nq,e.__scopeMenu);return(0,d.jsx)(nD.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tM,{present:r||i.open,children:(0,d.jsx)(nD.Slot,{scope:e.__scopeMenu,children:l.modal?(0,d.jsx)(n1,{...o,ref:t}):(0,d.jsx)(n2,{...o,ref:t})})})})}),n1=a.forwardRef((e,t)=>{let n=nB(nq,e.__scopeMenu),r=a.useRef(null),o=(0,c.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return tq(e)},[]),(0,d.jsx)(n5,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:s(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),n2=a.forwardRef((e,t)=>{let n=nB(nq,e.__scopeMenu);return(0,d.jsx)(n5,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),n9=(0,b.TL)("MenuContent.ScrollLock"),n5=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:u,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:v,onDismiss:g,disableOutsideScroll:y,...w}=e,b=nB(nq,n),x=nG(nq,n),E=nW(n),R=nz(n),C=nj(n),[A,S]=a.useState(null),M=a.useRef(null),k=(0,c.s)(t,M,b.onContentChange),T=a.useRef(0),O=a.useRef(""),P=a.useRef(0),j=a.useRef(null),I=a.useRef("right"),_=a.useRef(0),W=y?nM:a.Fragment;a.useEffect(()=>()=>window.clearTimeout(T.current),[]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:D()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:D()),N++,()=>{1===N&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),N--}},[]);let z=a.useCallback(e=>{var t,n;return I.current===(null==(t=j.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=j.current)?void 0:n.area)},[]);return(0,d.jsx)(nJ,{scope:n,searchRef:O,onItemEnter:a.useCallback(e=>{z(e)&&e.preventDefault()},[z]),onItemLeave:a.useCallback(e=>{var t;z(e)||(null==(t=M.current)||t.focus(),S(null))},[z]),onTriggerLeave:a.useCallback(e=>{z(e)&&e.preventDefault()},[z]),pointerGraceTimerRef:P,onPointerGraceIntentChange:a.useCallback(e=>{j.current=e},[]),children:(0,d.jsx)(W,{...y?{as:n9,allowPinchZoom:!0}:void 0,children:(0,d.jsx)(F,{asChild:!0,trapped:o,onMountAutoFocus:s(i,e=>{var t;e.preventDefault(),null==(t=M.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,d.jsx)(L,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:p,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:v,onDismiss:g,children:(0,d.jsx)(tW,{asChild:!0,...R,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:A,onCurrentTabStopIdChange:S,onEntryFocus:s(f,e=>{x.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,d.jsx)(tw,{role:"menu","aria-orientation":"vertical","data-state":rE(b.open),"data-radix-menu-content":"",dir:x.dir,...E,...w,ref:k,style:{outline:"none",...w.style},onKeyDown:s(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=O.current+e,o=C().filter(e=>!e.disabled),i=document.activeElement,l=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,l=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(o.map(e=>e.textValue),r,l),u=null==(n=o.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){O.current=t,window.clearTimeout(T.current),""!==t&&(T.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())})(e.key));let o=M.current;if(e.target!==o||!nL.includes(e.key))return;e.preventDefault();let i=C().filter(e=>!e.disabled).map(e=>e.ref.current);nT.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:s(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(T.current),O.current="")}),onPointerMove:s(e.onPointerMove,rA(e=>{let t=e.target,n=_.current!==e.clientX;e.currentTarget.contains(t)&&n&&(I.current=e.clientX>_.current?"right":"left",_.current=e.clientX)}))})})})})})})});n0.displayName=nq;var n4=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(v.sG.div,{role:"group",...r,ref:t})});n4.displayName="MenuGroup";var n6=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(v.sG.div,{...r,ref:t})});n6.displayName="MenuLabel";var n3="MenuItem",n7="menu.itemSelect",n8=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=a.useRef(null),l=nG(n3,e.__scopeMenu),u=nQ(n3,e.__scopeMenu),f=(0,c.s)(t,i),p=a.useRef(!1);return(0,d.jsx)(re,{...o,ref:f,disabled:n,onClick:s(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(n7,{bubbles:!0,cancelable:!0});e.addEventListener(n7,e=>null==r?void 0:r(e),{once:!0}),(0,v.hO)(e,t),t.defaultPrevented?p.current=!1:l.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:s(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:s(e.onKeyDown,e=>{let t=""!==u.searchRef.current;n||t&&" "===e.key||nk.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});n8.displayName=n3;var re=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,l=nQ(n3,n),u=nz(n),f=a.useRef(null),p=(0,c.s)(t,f),[h,m]=a.useState(!1),[g,y]=a.useState("");return a.useEffect(()=>{let e=f.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,d.jsx)(nD.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:g,children:(0,d.jsx)(tB,{asChild:!0,...u,focusable:!r,children:(0,d.jsx)(v.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:p,onPointerMove:s(e.onPointerMove,rA(e=>{r?l.onItemLeave(e):(l.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:s(e.onPointerLeave,rA(e=>l.onItemLeave(e))),onFocus:s(e.onFocus,()=>m(!0)),onBlur:s(e.onBlur,()=>m(!1))})})})}),rt=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,d.jsx)(rs,{scope:e.__scopeMenu,checked:n,children:(0,d.jsx)(n8,{role:"menuitemcheckbox","aria-checked":rR(n)?"mixed":n,...o,ref:t,"data-state":rC(n),onSelect:s(o.onSelect,()=>null==r?void 0:r(!!rR(n)||!n),{checkForDefaultPrevented:!1})})})});rt.displayName="MenuCheckboxItem";var rn="MenuRadioGroup",[rr,ro]=n_(rn,{value:void 0,onValueChange:()=>{}}),ri=a.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=M(r);return(0,d.jsx)(rr,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,d.jsx)(n4,{...o,ref:t})})});ri.displayName=rn;var rl="MenuRadioItem",ra=a.forwardRef((e,t)=>{let{value:n,...r}=e,o=ro(rl,e.__scopeMenu),i=n===o.value;return(0,d.jsx)(rs,{scope:e.__scopeMenu,checked:i,children:(0,d.jsx)(n8,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":rC(i),onSelect:s(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});ra.displayName=rl;var ru="MenuItemIndicator",[rs,rc]=n_(ru,{checked:!1}),rd=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=rc(ru,n);return(0,d.jsx)(tM,{present:r||rR(i.checked)||!0===i.checked,children:(0,d.jsx)(v.sG.span,{...o,ref:t,"data-state":rC(i.checked)})})});rd.displayName=ru;var rf=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(v.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});rf.displayName="MenuSeparator";var rp=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nW(n);return(0,d.jsx)(tE,{...o,...r,ref:t})});rp.displayName="MenuArrow";var rh="MenuSub",[rm,rv]=n_(rh),rg=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,i=nB(rh,t),l=nW(t),[u,s]=a.useState(null),[c,f]=a.useState(null),p=M(o);return a.useEffect(()=>(!1===i.open&&p(!1),()=>p(!1)),[i.open,p]),(0,d.jsx)(tp,{...l,children:(0,d.jsx)(nK,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:f,children:(0,d.jsx)(rm,{scope:t,contentId:V(),triggerId:V(),trigger:u,onTriggerChange:s,children:n})})})};rg.displayName=rh;var ry="MenuSubTrigger",rw=a.forwardRef((e,t)=>{let n=nB(ry,e.__scopeMenu),r=nG(ry,e.__scopeMenu),o=rv(ry,e.__scopeMenu),i=nQ(ry,e.__scopeMenu),l=a.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:f}=i,p={__scopeMenu:e.__scopeMenu},h=a.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return a.useEffect(()=>h,[h]),a.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),f(null)}},[u,f]),(0,d.jsx)(nV,{asChild:!0,...p,children:(0,d.jsx)(re,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":rE(n.open),...e,ref:(0,c.t)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:s(e.onPointerMove,rA(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||l.current||(i.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:s(e.onPointerLeave,rA(e=>{var t,r;h();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,l="right"===t,a=o[l?"left":"right"],s=o[l?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:a,y:o.top},{x:s,y:o.top},{x:s,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:s(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&nO[r.dir].includes(t.key)){var l;n.onOpenChange(!0),null==(l=n.content)||l.focus(),t.preventDefault()}})})})});rw.displayName=ry;var rb="MenuSubContent",rx=a.forwardRef((e,t)=>{let n=n$(nq,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nB(nq,e.__scopeMenu),l=nG(nq,e.__scopeMenu),u=rv(rb,e.__scopeMenu),f=a.useRef(null),p=(0,c.s)(t,f);return(0,d.jsx)(nD.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(tM,{present:r||i.open,children:(0,d.jsx)(nD.Slot,{scope:e.__scopeMenu,children:(0,d.jsx)(n5,{id:u.contentId,"aria-labelledby":u.triggerId,...o,ref:p,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;l.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:s(e.onFocusOutside,e=>{e.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:s(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:s(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=nP[l.dir].includes(e.key);if(t&&n){var r;i.onOpenChange(!1),null==(r=u.trigger)||r.focus(),e.preventDefault()}})})})})})});function rE(e){return e?"open":"closed"}function rR(e){return"indeterminate"===e}function rC(e){return rR(e)?"indeterminate":e?"checked":"unchecked"}function rA(e){return t=>"mouse"===t.pointerType?e(t):void 0}rx.displayName=rb;var rS="DropdownMenu",[rM,rk]=f(rS,[nF]),rT=nF(),[rL,rO]=rM(rS),rP=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:l,modal:u=!0}=e,s=rT(t),c=a.useRef(null),[f,p]=m({prop:o,defaultProp:null!=i&&i,onChange:l,caller:rS});return(0,d.jsx)(rL,{scope:t,triggerId:V(),triggerRef:c,contentId:V(),open:f,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,d.jsx)(nU,{...s,open:f,onOpenChange:p,dir:r,modal:u,children:n})})};rP.displayName=rS;var rN="DropdownMenuTrigger",rD=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=rO(rN,n),l=rT(n);return(0,d.jsx)(nV,{asChild:!0,...l,children:(0,d.jsx)(v.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,c.t)(t,i.triggerRef),onPointerDown:s(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:s(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});rD.displayName=rN;var rj=e=>{let{__scopeDropdownMenu:t,...n}=e,r=rT(t);return(0,d.jsx)(nZ,{...r,...n})};rj.displayName="DropdownMenuPortal";var rI="DropdownMenuContent",r_=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rO(rI,n),i=rT(n),l=a.useRef(!1);return(0,d.jsx)(n0,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:s(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=o.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:s(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});r_.displayName=rI;var rF=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(n4,{...o,...r,ref:t})});rF.displayName="DropdownMenuGroup";var rW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(n6,{...o,...r,ref:t})});rW.displayName="DropdownMenuLabel";var rz=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(n8,{...o,...r,ref:t})});rz.displayName="DropdownMenuItem";var rK=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rt,{...o,...r,ref:t})});rK.displayName="DropdownMenuCheckboxItem";var rB=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(ri,{...o,...r,ref:t})});rB.displayName="DropdownMenuRadioGroup";var rH=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(ra,{...o,...r,ref:t})});rH.displayName="DropdownMenuRadioItem";var rG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rd,{...o,...r,ref:t})});rG.displayName="DropdownMenuItemIndicator";var rU=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rf,{...o,...r,ref:t})});rU.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rp,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var rV=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rw,{...o,...r,ref:t})});rV.displayName="DropdownMenuSubTrigger";var rX=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=rT(n);return(0,d.jsx)(rx,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rX.displayName="DropdownMenuSubContent";var rY=rP,r$=rD,rZ=rj,rq=r_,rJ=rF,rQ=rW,r0=rz,r1=rK,r2=rB,r9=rH,r5=rG,r4=rU,r6=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,l=rT(t),[a,u]=m({prop:r,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,d.jsx)(rg,{...l,open:a,onOpenChange:u,children:n})},r3=rV,r7=rX},2098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2148:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]])},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),o=n(7650),i=n(9708),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:n,strokeWidth:u?24*Number(a)/Number(o):a,className:i("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:u,...s}=n;return(0,r.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...s})});return n.displayName=o(e),n}}}]);