(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[766],{3999:(e,a,s)=>{"use strict";s.d(a,{cn:()=>l});var t=s(2596),r=s(9688);function l(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},4515:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>h});var t=s(5155),r=s(2115),l=s(7168),i=s(8482),n=s(4835),d=s(9869),o=s(3311),c=s(1788),m=s(5695),u=s(6414),x=s(6766);function h(){let[e,a]=(0,r.useState)(null),[s,h]=(0,r.useState)(!1),[f,g]=(0,r.useState)(null),[b,p]=(0,r.useState)(null),[j,v]=(0,r.useState)(null),[N,y]=(0,r.useState)(!1),w=(0,m.useRouter)(),k=(0,u.U)(),C=async()=>{await k.auth.signOut(),w.push("/")},D=async e=>{if(a(e),g(null),p(null),v(null),e)try{let a=new FormData;a.append("file",e);let s=await fetch("/api/upload",{method:"POST",body:a});if(!s.ok){let e=await s.json();throw Error(e.error||"Upload failed")}let t=await s.json();v(t.image)}catch(e){g(e instanceof Error?e.message:"Upload failed")}},I=async()=>{if(!j)return void g("Please upload an image first");h(!0),g(null),p(null);try{let e=await fetch("/api/process",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({imageData:j,prompt:"Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution"})});if(!e.ok){let a=await e.json();throw Error(a.error||"Processing failed")}let a=await e.json();p(a)}catch(e){g(e instanceof Error?e.message:"Processing failed")}finally{h(!1)}},T=e&&!s;return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",children:[(0,t.jsx)("div",{className:"border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-4 flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900 dark:text-slate-100",children:"Tears of the Left"}),(0,t.jsxs)(l.$,{onClick:C,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),"Sign Out"]})]})}),(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(i.Zp,{className:"border-2 border-dashed transition-colors ".concat(N?"border-slate-500 dark:border-slate-400 bg-slate-50 dark:bg-slate-800/50":"border-slate-300 dark:border-slate-600 bg-transparent"),onDragOver:e=>{e.preventDefault(),y(!0)},onDragLeave:e=>{e.preventDefault(),y(!1)},onDrop:e=>{e.preventDefault(),y(!1);let a=e.dataTransfer.files;if(a.length>0){let e=a[0];e.type.startsWith("image/")?D(e):g("Please drop an image file")}},children:(0,t.jsx)(i.Wu,{className:"p-8",children:e?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-slate-900 dark:text-slate-100",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>D(null),disabled:s,children:"Change"})]}),(0,t.jsx)(l.$,{onClick:I,disabled:!T,className:"w-full h-12 text-lg bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900",size:"lg",children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-slate-900 mr-2"}),"Creating tears..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Transform Image"]})})]}):(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-slate-400"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100",children:"Upload your image"}),(0,t.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:N?"Drop your image here":"Drag and drop an image or click to choose"}),(0,t.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-500",children:"Transform with the “Tears of the Left” effect"})]}),(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var a;return D((null==(a=e.target.files)?void 0:a[0])||null)},className:"hidden",id:"file-upload",disabled:s}),(0,t.jsx)(l.$,{asChild:!0,className:"bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900",disabled:s,children:(0,t.jsx)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:"Choose Image"})})]})})}),f&&(0,t.jsx)(i.Zp,{className:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20",children:(0,t.jsx)(i.Wu,{className:"p-4",children:(0,t.jsx)("p",{className:"text-red-700 dark:text-red-400 text-center",children:f})})}),s&&(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2",children:"Creating your masterpiece..."}),(0,t.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:"The AI is painting tears of emotion onto your image"})]}),(0,t.jsx)("div",{className:"relative rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800 h-96 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"space-y-4 text-center",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"w-16 h-16 mx-auto relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 rounded-full bg-blue-200 dark:bg-blue-800 animate-ping opacity-75"}),(0,t.jsx)("div",{className:"absolute inset-2 rounded-full bg-blue-300 dark:bg-blue-700 animate-pulse"}),(0,t.jsx)("div",{className:"absolute inset-4 rounded-full bg-blue-400 dark:bg-blue-600"})]}),(0,t.jsxs)("div",{className:"absolute -left-8 top-8 space-y-2",children:[(0,t.jsx)("div",{className:"w-2 h-3 bg-blue-400 dark:bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,t.jsx)("div",{className:"w-2 h-3 bg-blue-300 dark:bg-blue-600 rounded-full animate-bounce opacity-70",style:{animationDelay:"0.2s"}}),(0,t.jsx)("div",{className:"w-2 h-3 bg-blue-200 dark:bg-blue-700 rounded-full animate-bounce opacity-50",style:{animationDelay:"0.4s"}})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Transforming with emotion..."}),(0,t.jsxs)("div",{className:"flex justify-center space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})})}),b&&!s&&(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2",children:"Your transformed image"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:"The “Tears of the Left” effect has been applied"})]}),(0,t.jsx)("div",{className:"relative rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800",children:(0,t.jsx)(x.default,{src:b.editedImageUrl,alt:"Transformed image",width:1024,height:1024,className:"w-full h-auto max-h-96 object-contain mx-auto",unoptimized:!0})}),(0,t.jsxs)(l.$,{onClick:()=>{let e=document.createElement("a");e.href=b.editedImageUrl,e.download="tears-of-the-left-".concat(Date.now(),".png"),document.body.appendChild(e),e.click(),document.body.removeChild(e)},className:"w-full bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Download Image"]})]})})})]})})]})}},6414:(e,a,s)=>{"use strict";s.d(a,{U:()=>r});var t=s(3865);function r(){return(0,t.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},6928:(e,a,s)=>{Promise.resolve().then(s.bind(s,4515))},7168:(e,a,s)=>{"use strict";s.d(a,{$:()=>o});var t=s(5155),r=s(2115),l=s(9708),i=s(2085),n=s(3999);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,a)=>{let{className:s,variant:r,size:i,asChild:o=!1,...c}=e,m=o?l.DX:"button";return(0,t.jsx)(m,{className:(0,n.cn)(d({variant:r,size:i,className:s})),ref:a,...c})});o.displayName="Button"},8482:(e,a,s)=>{"use strict";s.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var t=s(5155),r=s(2115),l=s(3999);let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...r})});i.displayName="Card";let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});n.displayName="CardHeader";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("font-semibold leading-none tracking-tight",s),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",s),...r})});c.displayName="CardContent",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"}},e=>{e.O(0,[352,30,97,441,964,358],()=>e(e.s=6928)),_N_E=e.O()}]);