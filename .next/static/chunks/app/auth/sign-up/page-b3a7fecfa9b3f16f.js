(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[303],{439:(e,r,t)=>{Promise.resolve().then(t.bind(t,4505))},2714:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(5155),a=t(2115),n=t(968),i=t(2085),d=t(3999);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,d.cn)(l(),t),...a})});o.displayName=n.b.displayName},3999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},4505:(e,r,t)=>{"use strict";t.d(r,{SignUpForm:()=>p});var s=t(5155),a=t(3999),n=t(6414),i=t(7168),d=t(8482),l=t(9852),o=t(2714),c=t(6874),u=t.n(c),m=t(5695),f=t(2115);function p(e){let{className:r,...t}=e,[c,p]=(0,f.useState)(""),[x,h]=(0,f.useState)(""),[g,v]=(0,f.useState)(""),[b,w]=(0,f.useState)(null),[y,j]=(0,f.useState)(null),[N,C]=(0,f.useState)(!1),I=(0,m.useRouter)(),R=async e=>{e.preventDefault();let r=(0,n.U)();if(C(!0),w(null),j(null),x!==g){w("Passwords do not match"),C(!1);return}try{let{error:e}=await r.auth.signUp({email:c,password:x,options:{emailRedirectTo:"".concat(window.location.origin,"/editor")}});if(e)throw e;j("Account created! Please check your email to verify your account."),setTimeout(()=>I.push("/editor"),2e3)}catch(e){w(e instanceof Error?e.message:"An error occurred")}finally{C(!1)}};return(0,s.jsx)("div",{className:(0,a.cn)("flex flex-col gap-6",r),...t,children:(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{className:"text-2xl",children:"Sign up"}),(0,s.jsx)(d.BT,{children:"Create a new account"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:R,children:[(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0,value:c,onChange:e=>p(e.target.value)})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(o.J,{htmlFor:"password",children:"Password"})}),(0,s.jsx)(l.p,{id:"password",type:"password",required:!0,value:x,onChange:e=>h(e.target.value)})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(o.J,{htmlFor:"repeat-password",children:"Repeat Password"})}),(0,s.jsx)(l.p,{id:"repeat-password",type:"password",required:!0,value:g,onChange:e=>v(e.target.value)})]}),b&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:b}),y&&(0,s.jsx)("p",{className:"text-sm text-green-500",children:y}),(0,s.jsx)(i.$,{type:"submit",className:"w-full",disabled:N,children:N?"Creating an account...":"Sign up"})]}),(0,s.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Already have an account?"," ",(0,s.jsx)(u(),{href:"/auth/login",className:"underline underline-offset-4",children:"Login"})]})]})})]})})}},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6414:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(3865);function a(){return(0,s.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155),a=t(2115),n=t(9708),i=t(2085),d=t(3999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:o=!1,...c}=e,u=o?n.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(l({variant:a,size:i,className:t})),ref:r,...c})});o.displayName="Button"},8482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>d});var s=t(5155),a=t(2115),n=t(3999);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...a})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",t),...a})});l.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},9852:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155),a=t(2115),n=t(3999);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...i})});i.displayName="Input"}},e=>{e.O(0,[352,30,11,441,964,358],()=>e(e.s=439)),_N_E=e.O()}]);