(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{2714:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(5155),a=t(2115),n=t(968),i=t(2085),o=t(3999);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,o.cn)(d(),t),...a})});l.displayName=n.b.displayName},3999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},4397:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>p});var s=t(5155),a=t(3999),n=t(6414),i=t(7168),o=t(8482),d=t(9852),l=t(2714),c=t(6874),u=t.n(c),f=t(5695),m=t(2115);function p(e){let{className:r,...t}=e,[c,p]=(0,m.useState)(""),[x,h]=(0,m.useState)(""),[g,v]=(0,m.useState)(null),[b,w]=(0,m.useState)(!1),y=(0,f.useRouter)(),N=async e=>{e.preventDefault();let r=(0,n.U)();w(!0),v(null);try{let{error:e}=await r.auth.signInWithPassword({email:c,password:x});if(e)throw e;y.push("/editor")}catch(e){v(e instanceof Error?e.message:"An error occurred")}finally{w(!1)}};return(0,s.jsx)("div",{className:(0,a.cn)("flex flex-col gap-6",r),...t,children:(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{className:"text-2xl",children:"Login"}),(0,s.jsx)(o.BT,{children:"Enter your email below to login to your account"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("form",{onSubmit:N,children:[(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0,value:c,onChange:e=>p(e.target.value)})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.J,{htmlFor:"password",children:"Password"}),(0,s.jsx)(u(),{href:"/auth/forgot-password",className:"ml-auto inline-block text-sm underline-offset-4 hover:underline",children:"Forgot your password?"})]}),(0,s.jsx)(d.p,{id:"password",type:"password",required:!0,value:x,onChange:e=>h(e.target.value)})]}),g&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:g}),(0,s.jsx)(i.$,{type:"submit",className:"w-full",disabled:b,children:b?"Logging in...":"Login"})]}),(0,s.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Don't have an account?"," ",(0,s.jsx)(u(),{href:"/auth/sign-up",className:"underline underline-offset-4",children:"Sign up"})]})]})})]})})}},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6414:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(3865);function a(){return(0,s.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(5155),a=t(2115),n=t(9708),i=t(2085),o=t(3999);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:l=!1,...c}=e,u=l?n.DX:"button";return(0,s.jsx)(u,{className:(0,o.cn)(d({variant:a,size:i,className:t})),ref:r,...c})});l.displayName="Button"},7636:(e,r,t)=>{Promise.resolve().then(t.bind(t,4397))},8482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>o});var s=t(5155),a=t(2115),n=t(3999);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...a})});i.displayName="Card";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});o.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});l.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},9852:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155),a=t(2115),n=t(3999);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...i})});i.displayName="Input"}},e=>{e.O(0,[352,30,11,441,964,358],()=>e(e.s=7636)),_N_E=e.O()}]);