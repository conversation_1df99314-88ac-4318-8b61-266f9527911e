(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[794],{2714:(e,r,s)=>{"use strict";s.d(r,{J:()=>o});var t=s(5155),a=s(2115),n=s(968),i=s(2085),d=s(3999);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)(n.b,{ref:r,className:(0,d.cn)(l(),s),...a})});o.displayName=n.b.displayName},3047:(e,r,s)=>{"use strict";s.d(r,{ForgotPasswordForm:()=>f});var t=s(5155),a=s(3999),n=s(6414),i=s(7168),d=s(8482),l=s(9852),o=s(2714),c=s(6874),u=s.n(c),m=s(2115);function f(e){let{className:r,...s}=e,[c,f]=(0,m.useState)(""),[p,x]=(0,m.useState)(null),[h,g]=(0,m.useState)(!1),[v,b]=(0,m.useState)(!1),w=async e=>{e.preventDefault();let r=(0,n.U)();b(!0),x(null);try{let{error:e}=await r.auth.resetPasswordForEmail(c,{redirectTo:"".concat(window.location.origin,"/auth/update-password")});if(e)throw e;g(!0)}catch(e){x(e instanceof Error?e.message:"An error occurred")}finally{b(!1)}};return(0,t.jsx)("div",{className:(0,a.cn)("flex flex-col gap-6",r),...s,children:h?(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{className:"text-2xl",children:"Check Your Email"}),(0,t.jsx)(d.BT,{children:"Password reset instructions sent"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"If you registered using your email and password, you will receive a password reset email."})})]}):(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{className:"text-2xl",children:"Reset Your Password"}),(0,t.jsx)(d.BT,{children:"Type in your email and we'll send you a link to reset your password"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsxs)("form",{onSubmit:w,children:[(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",required:!0,value:c,onChange:e=>f(e.target.value)})]}),p&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:p}),(0,t.jsx)(i.$,{type:"submit",className:"w-full",disabled:v,children:v?"Sending...":"Send reset email"})]}),(0,t.jsxs)("div",{className:"mt-4 text-center text-sm",children:["Already have an account?"," ",(0,t.jsx)(u(),{href:"/auth/login",className:"underline underline-offset-4",children:"Login"})]})]})})]})})}},3999:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var t=s(2596),a=s(9688);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}},4323:(e,r,s)=>{Promise.resolve().then(s.bind(s,3047))},6414:(e,r,s)=>{"use strict";s.d(r,{U:()=>a});var t=s(3865);function a(){return(0,t.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},7168:(e,r,s)=>{"use strict";s.d(r,{$:()=>o});var t=s(5155),a=s(2115),n=s(9708),i=s(2085),d=s(3999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:s,variant:a,size:i,asChild:o=!1,...c}=e,u=o?n.DX:"button";return(0,t.jsx)(u,{className:(0,d.cn)(l({variant:a,size:i,className:s})),ref:r,...c})});o.displayName="Button"},8482:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>d});var t=s(5155),a=s(2115),n=s(3999);let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...a})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});d.displayName="CardHeader";let l=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",s),...a})});l.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",s),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},9852:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(5155),a=s(2115),n=s(3999);let i=a.forwardRef((e,r)=>{let{className:s,type:a,...i}=e;return(0,t.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...i})});i.displayName="Input"}},e=>{e.O(0,[352,30,11,441,964,358],()=>e(e.s=4323)),_N_E=e.O()}]);