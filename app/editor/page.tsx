'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, Sparkles, LogOut, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import Image from 'next/image';

// Hardcoded prompt for "Tears of the left" effect
const HARDCODED_PROMPT = "Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution";

interface ProcessingResult {
  editedImageUrl: string;
  originalPrompt: string;
  processedAt: string;
  model: string;
}

export default function EditorPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [uploadedImageData, setUploadedImageData] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.startsWith('image/')) {
        handleFileSelect(file);
      } else {
        setError('Please drop an image file');
      }
    }
  };

  const handleFileSelect = async (file: File | null) => {
    setSelectedFile(file);
    setError(null);
    setResult(null);
    setUploadedImageData(null);

    if (file) {
      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        const data = await response.json();
        setUploadedImageData(data.image);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Upload failed');
      }
    }
  };

  const handleProcess = async () => {
    if (!uploadedImageData) {
      setError('Please upload an image first');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: uploadedImageData,
          prompt: HARDCODED_PROMPT,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Processing failed');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const canProcess = selectedFile && !isProcessing;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <div className="border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
            Tears of the Left
          </h1>
          <Button 
            onClick={handleSignOut}
            variant="outline" 
            size="sm"
            className="flex items-center gap-2"
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Upload Section */}
          <Card 
            className={`border-2 border-dashed transition-colors ${
              isDragOver 
                ? 'border-slate-500 dark:border-slate-400 bg-slate-50 dark:bg-slate-800/50' 
                : 'border-slate-300 dark:border-slate-600 bg-transparent'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <CardContent className="p-8">
              {!selectedFile ? (
                <div className="text-center space-y-4">
                  <div className="mx-auto w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                    <Upload className="h-8 w-8 text-slate-400" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                      Upload your image
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      {isDragOver 
                        ? 'Drop your image here' 
                        : 'Drag and drop an image or click to choose'
                      }
                    </p>
                    <p className="text-xs text-slate-500 dark:text-slate-500">
                      Transform with the &ldquo;Tears of the Left&rdquo; effect
                    </p>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
                    className="hidden"
                    id="file-upload"
                    disabled={isProcessing}
                  />
                  <Button 
                    asChild 
                    className="bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900"
                    disabled={isProcessing}
                  >
                    <label htmlFor="file-upload" className="cursor-pointer">
                      Choose Image
                    </label>
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <Upload className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900 dark:text-slate-100">
                          {selectedFile.name}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFileSelect(null)}
                      disabled={isProcessing}
                    >
                      Change
                    </Button>
                  </div>
                  
                  <Button
                    onClick={handleProcess}
                    disabled={!canProcess}
                    className="w-full h-12 text-lg bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900"
                    size="lg"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-slate-900 mr-2" />
                        Creating tears...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-5 w-5 mr-2" />
                        Transform Image
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Card className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
              <CardContent className="p-4">
                <p className="text-red-700 dark:text-red-400 text-center">{error}</p>
              </CardContent>
            </Card>
          )}

          {/* Loading Animation */}
          {isProcessing && (
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                      Creating your masterpiece...
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      The AI is painting tears of emotion onto your image
                    </p>
                  </div>
                  
                  {/* Loading Animation */}
                  <div className="relative rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800 h-96 flex items-center justify-center">
                    <div className="space-y-4 text-center">
                      {/* Animated Tear Drops */}
                      <div className="relative">
                        <div className="w-16 h-16 mx-auto relative">
                          <div className="absolute inset-0 rounded-full bg-blue-200 dark:bg-blue-800 animate-ping opacity-75"></div>
                          <div className="absolute inset-2 rounded-full bg-blue-300 dark:bg-blue-700 animate-pulse"></div>
                          <div className="absolute inset-4 rounded-full bg-blue-400 dark:bg-blue-600"></div>
                        </div>
                        
                        {/* Falling tear drops */}
                        <div className="absolute -left-8 top-8 space-y-2">
                          <div className="w-2 h-3 bg-blue-400 dark:bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0s'}}></div>
                          <div className="w-2 h-3 bg-blue-300 dark:bg-blue-600 rounded-full animate-bounce opacity-70" style={{animationDelay: '0.2s'}}></div>
                          <div className="w-2 h-3 bg-blue-200 dark:bg-blue-700 rounded-full animate-bounce opacity-50" style={{animationDelay: '0.4s'}}></div>
                        </div>
                      </div>
                      
                      {/* Progress text */}
                      <div className="space-y-2">
                        <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          Transforming with emotion...
                        </div>
                        <div className="flex justify-center space-x-1">
                          <div className="w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce" style={{animationDelay: '0s'}}></div>
                          <div className="w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-slate-400 dark:bg-slate-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Result Display */}
          {result && !isProcessing && (
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                      Your transformed image
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      The &ldquo;Tears of the Left&rdquo; effect has been applied
                    </p>
                  </div>
                  
                  <div className="relative rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800">
                    <Image
                      src={result.editedImageUrl}
                      alt="Transformed image"
                      width={1024}
                      height={1024}
                      className="w-full h-auto max-h-96 object-contain mx-auto"
                      unoptimized={true}
                    />
                  </div>
                  
                  <Button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = result.editedImageUrl;
                      link.download = `tears-of-the-left-${Date.now()}.png`;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    className="w-full bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Image
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}