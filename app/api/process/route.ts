import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import OpenAI from 'openai';
import sharp from 'sharp';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { imageData, prompt } = await request.json();

    if (!imageData || !prompt) {
      return NextResponse.json({ 
        error: 'Image data and prompt are required' 
      }, { status: 400 });
    }

    // Validate prompt length for gpt-image-1
    if (prompt.length > 32000) {
      return NextResponse.json({ 
        error: 'Prompt too long. Maximum 32000 characters for gpt-image-1.' 
      }, { status: 400 });
    }

    // Convert base64 image to buffer and ensure it's in the right format
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Convert to PNG format using Sharp for better compatibility
    const pngBuffer = await sharp(imageBuffer)
      .png()
      .resize(1024, 1024, { 
        fit: 'inside', 
        withoutEnlargement: true,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .toBuffer();

    // Create a proper File stream for OpenAI
    const imageBlob = new Blob([pngBuffer], { type: 'image/png' });
    const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });

    console.log('Sending image to OpenAI:', {
      originalSize: imageBuffer.length,
      processedSize: pngBuffer.length,
      prompt: prompt,
      fileType: imageFile.type
    });

    // Use gpt-image-1 for image editing
    console.log('Using gpt-image-1 for image transformation...');
    const response = await openai.images.edit({
      model: "gpt-image-1",
      image: imageFile,
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      output_format: "png",
      input_fidelity: "high",
      quality: "high"
    });
    
    const modelUsed = "gpt-image-1";

    console.log('Model used:', modelUsed);
    console.log('Response data length:', response.data?.length);

    let editedImageUrl = null;
    
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      const firstResult = response.data[0];
      
      // gpt-image-1 returns base64-encoded images
      if (firstResult.b64_json) {
        // Convert base64 to data URL
        editedImageUrl = `data:image/png;base64,${firstResult.b64_json}`;
        console.log('Generated data URL from base64 for gpt-image-1');
      }
    }

    if (!editedImageUrl) {
      console.error('No image data found in OpenAI response. Full response:', JSON.stringify(response, null, 2));
      throw new Error(`No edited image data found. Model: ${modelUsed}, Response keys: ${response.data?.[0] ? Object.keys(response.data[0]).join(', ') : 'no data'}`);
    }

    return NextResponse.json({
      success: true,
      editedImageUrl,
      originalPrompt: prompt,
      processedAt: new Date().toISOString(),
      model: modelUsed
    });

  } catch (error) {
    console.error('Processing error:', error);
    
    // Handle specific OpenAI errors
    if (error instanceof OpenAI.APIError) {
      if (error.status === 401) {
        return NextResponse.json({ 
          error: 'Invalid API key configuration' 
        }, { status: 500 });
      }
      if (error.status === 429) {
        return NextResponse.json({ 
          error: 'API rate limit exceeded. Please try again later.' 
        }, { status: 429 });
      }
      if (error.status === 400) {
        return NextResponse.json({ 
          error: 'Invalid image or prompt. Please check your input and try again.' 
        }, { status: 400 });
      }
      return NextResponse.json({ 
        error: `API error: ${error.message}` 
      }, { status: error.status || 500 });
    }

    return NextResponse.json({ 
      error: 'Processing failed. Please try again.' 
    }, { status: 500 });
  }
}