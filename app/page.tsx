import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";

export default async function Home() {
  // Check if user is already authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // If authenticated, redirect to editor
  if (user) {
    redirect("/editor");
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100">
            Tears of the Left
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Transform your images with AI
          </p>
        </div>

        {/* Auth Forms */}
        <div className="space-y-6">
          <AuthTabs />
        </div>

        {/* Footer */}
        <div className="flex items-center justify-center">
          <ThemeSwitcher />
        </div>
      </div>
    </main>
  );
}

function AuthTabs() {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-center">Welcome</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Button asChild variant="outline">
              <a href="/auth/login">Sign In</a>
            </Button>
            <Button asChild>
              <a href="/auth/sign-up">Sign Up</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
